using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using static SmartPanelTool.GeometryHelper;

namespace SmartPanelTool
{
    /// <summary>
    /// 排板方向枚举
    /// </summary>
    public enum PanelDirection
    {
        /// <summary>
        /// 从左往右排版
        /// </summary>
        LeftToRight,

        /// <summary>
        /// 从右往左排版
        /// </summary>
        RightToLeft,

        /// <summary>
        /// 从中间向两边排版
        /// </summary>
        CenterToSides
    }

    /// <summary>
    /// 主窗体 - 复制原插件的UI设计
    /// </summary>
    public partial class MainForm : Form
    {
        #region 私有字段
        private SmartPanelGenerator _generator;
        private bool _isProcessing = false;
        #endregion

        #region 构造函数
        public MainForm()
        {
            InitializeComponent();
            _generator = new SmartPanelGenerator();
            
            // 设置默认选中的排版方向
            radioLeftToRight.Checked = true;
        }
        #endregion

        #region 事件处理
        private void MainForm_Load(object sender, EventArgs e)
        {
            try
            {
                // 设置默认值 - 与原插件保持一致
                txtStartDistance.Text = "0";
                txtPanelWidth.Text = "1000";
                txtTopGap.Text = "5";
                txtBottomGap.Text = "10";
                txtWindowGap.Text = "5";
                txtDoorTopGap.Text = "5";

                // 添加输入验证事件
                AttachValidationEvents();

                UpdateSelectionInfo();
                UpdateUIState();
            }
            catch (Exception ex)
            {
                ShowError("初始化窗体时发生错误", ex);
            }
        }

        /// <summary>
        /// 附加输入验证事件
        /// </summary>
        private void AttachValidationEvents()
        {
            // 为数值输入框添加验证事件
            txtPanelWidth.Leave += ValidatePositiveNumericInput;
            txtTopGap.Leave += ValidateNonNegativeNumericInput;
            txtBottomGap.Leave += ValidateNonNegativeNumericInput;
            txtWindowGap.Leave += ValidateNonNegativeNumericInput;
            txtDoorTopGap.Leave += ValidateNonNegativeNumericInput;
        }

        /// <summary>
        /// 验证正数输入
        /// </summary>
        private void ValidatePositiveNumericInput(object sender, EventArgs e)
        {
            TextBox textBox = sender as TextBox;
            if (textBox != null && !string.IsNullOrWhiteSpace(textBox.Text))
            {
                if (!double.TryParse(textBox.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double value) || value <= 0)
                {
                    MessageBox.Show($"请输入大于0的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBox.Focus();
                    textBox.SelectAll();
                }
            }
        }

        /// <summary>
        /// 验证非负数输入
        /// </summary>
        private void ValidateNonNegativeNumericInput(object sender, EventArgs e)
        {
            TextBox textBox = sender as TextBox;
            if (textBox != null && !string.IsNullOrWhiteSpace(textBox.Text))
            {
                if (!double.TryParse(textBox.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double value) || value < 0)
                {
                    MessageBox.Show($"请输入非负数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBox.Focus();
                    textBox.SelectAll();
                }
            }
        }
        #endregion

        #region 选择事件处理
        private void btnSelectOutline_Click(object sender, EventArgs e)
        {
            HandleSelection(() => _generator.SetOutline(), "未能选择有效的轮廓线", "选择轮廓线");
        }

        private void btnSelectWindow_Click(object sender, EventArgs e)
        {
            HandleSelection(() => _generator.AddWindows(), "未能选择有效的窗洞口", "选择窗洞口");
        }

        private void btnSelectDoor_Click(object sender, EventArgs e)
        {
            HandleSelection(() => _generator.AddDoors(), "未能选择有效的门洞口", "选择门洞口");
        }

        /// <summary>
        /// 处理选择操作的通用方法
        /// </summary>
        private void HandleSelection(Func<bool> selectionAction, string errorMessage, string operationName)
        {
            try
            {
                this.Hide();
                bool success = selectionAction();
                this.Show();

                if (success)
                {
                    UpdateSelectionInfo();
                    UpdateUIState();
                }
                else
                {
                    ShowError(errorMessage, null);
                }
            }
            catch (Exception ex)
            {
                this.Show();
                ShowError($"{operationName}时发生错误", ex);
            }
        }
        #endregion

        #region UI状态管理
        /// <summary>
        /// 更新选择信息显示
        /// </summary>
        private void UpdateSelectionInfo()
        {
            try
            {
                // 更新轮廓线状态
                if (_generator.HasOutline)
                {
                    lblOutlineStatus.Text = "已选择轮廓线";
                    lblOutlineStatus.ForeColor = Color.Green;
                }
                else
                {
                    lblOutlineStatus.Text = "未选择轮廓线";
                    lblOutlineStatus.ForeColor = Color.Red;
                }

                // 更新窗洞口状态
                int windowCount = _generator.WindowCount;
                if (windowCount > 0)
                {
                    lblWindowStatus.Text = $"已选择 {windowCount} 个窗洞口";
                    lblWindowStatus.ForeColor = Color.Green;
                }
                else
                {
                    lblWindowStatus.Text = "未选择窗洞口";
                    lblWindowStatus.ForeColor = Color.Gray;
                }

                // 更新门洞口状态
                int doorCount = _generator.DoorCount;
                if (doorCount > 0)
                {
                    lblDoorStatus.Text = $"已选择 {doorCount} 个门洞口";
                    lblDoorStatus.ForeColor = Color.Green;
                }
                else
                {
                    lblDoorStatus.Text = "未选择门洞口";
                    lblDoorStatus.ForeColor = Color.Gray;
                }
            }
            catch (Exception ex)
            {
                ShowError("更新选择信息时发生错误", ex);
            }
        }

        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUIState()
        {
            bool canGenerate = _generator.HasOutline && !_isProcessing;

            btnGenerate.Enabled = canGenerate;
            btnSelectOutline.Enabled = !_isProcessing;
            btnSelectWindow.Enabled = !_isProcessing;
            btnSelectDoor.Enabled = !_isProcessing;

            // 更新按钮文本
            if (_isProcessing)
            {
                btnGenerate.Text = "处理中...";
            }
            else
            {
                btnGenerate.Text = "生成矩形板";
            }
        }

        /// <summary>
        /// 显示错误信息
        /// </summary>
        private void ShowError(string message, Exception ex)
        {
            string fullMessage = message;
            if (ex != null)
            {
                fullMessage += $"\n详细信息: {ex.Message}";
            }
            MessageBox.Show(fullMessage, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        /// <summary>
        /// 显示成功信息
        /// </summary>
        private void ShowSuccess(string message)
        {
            MessageBox.Show(message, "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        #endregion

        #region 生成处理
        /// <summary>
        /// 验证并应用参数设置
        /// </summary>
        /// <returns>是否验证成功</returns>
        private bool ValidateAndApplyParameters()
        {
            try
            {
                // 验证并设置起始距离
                if (!double.TryParse(txtStartDistance.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double startDistance))
                {
                    ShowError("起始距离必须是有效的数值", null);
                    txtStartDistance.Focus();
                    return false;
                }
                _generator.StartDistance = startDistance;

                // 验证并设置板宽
                if (!double.TryParse(txtPanelWidth.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double panelWidth) || panelWidth <= 0)
                {
                    ShowError("板宽必须是大于0的数值", null);
                    txtPanelWidth.Focus();
                    return false;
                }
                _generator.PanelWidth = panelWidth;

                // 验证并设置顶部间隙
                if (!double.TryParse(txtTopGap.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double topGap) || topGap < 0)
                {
                    ShowError("顶部间隙必须是非负数值", null);
                    txtTopGap.Focus();
                    return false;
                }
                _generator.TopGap = topGap;

                // 验证并设置底部间隙
                if (!double.TryParse(txtBottomGap.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double bottomGap) || bottomGap < 0)
                {
                    ShowError("底部间隙必须是非负数值", null);
                    txtBottomGap.Focus();
                    return false;
                }
                _generator.BottomGap = bottomGap;

                // 验证并设置窗户间隙
                if (!double.TryParse(txtWindowGap.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double windowGap) || windowGap < 0)
                {
                    ShowError("窗户间隙必须是非负数值", null);
                    txtWindowGap.Focus();
                    return false;
                }
                _generator.WindowGap = windowGap;

                // 验证并设置门顶部间隙
                if (!double.TryParse(txtDoorTopGap.Text, NumberStyles.Float, CultureInfo.InvariantCulture, out double doorTopGap) || doorTopGap < 0)
                {
                    ShowError("门顶部间隙必须是非负数值", null);
                    txtDoorTopGap.Focus();
                    return false;
                }
                _generator.DoorTopGap = doorTopGap;

                return true;
            }
            catch (Exception ex)
            {
                ShowError("验证参数时发生错误", ex);
                return false;
            }
        }

        private void GeneratePanels(PanelDirection direction)
        {
            if (_isProcessing)
            {
                return;
            }

            if (!_generator.HasOutline)
            {
                ShowError("请先选择轮廓线", null);
                return;
            }

            // 验证并应用参数设置
            if (!ValidateAndApplyParameters())
            {
                return;
            }

            _generator.Direction = direction;
            _isProcessing = true;
            UpdateUIState();

            try
            {
                // 生成矩形板
                int count = _generator.GeneratePanels();

                if (count > 0)
                {
                    ShowSuccess($"成功生成{count}个矩形板");

                    // 显示可视化结果
                    ShowVisualizationResult();

                    // 生成成功，关闭窗体
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                    return;
                }
                else
                {
                    ShowError("未能生成任何矩形板，请检查参数设置和轮廓线", null);
                }
            }
            catch (Exception ex)
            {
                ShowError("生成矩形板时发生错误", ex);
            }
            finally
            {
                _isProcessing = false;
                UpdateUIState();
            }
        }
        #endregion

        #region 按钮事件处理
        private void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                // 清理资源
                _generator?.ClearSelection();
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                ShowError("关闭窗体时发生错误", ex);
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }

        private void btnGenerate_Click(object sender, EventArgs e)
        {
            try
            {
                // 根据选中的单选按钮决定排版方向
                PanelDirection direction = GetSelectedDirection();
                GeneratePanels(direction);
            }
            catch (Exception ex)
            {
                ShowError("启动生成过程时发生错误", ex);
            }
        }

        /// <summary>
        /// 获取选中的排版方向
        /// </summary>
        /// <returns>排版方向</returns>
        private PanelDirection GetSelectedDirection()
        {
            if (radioRightToLeft.Checked)
                return PanelDirection.RightToLeft;
            else if (radioCenterToSides.Checked)
                return PanelDirection.CenterToSides;
            else
                return PanelDirection.LeftToRight; // 默认值
        }
        #endregion

        #region 窗体生命周期
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                // 清理资源
                _generator?.ClearSelection();
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止窗体关闭
                System.Diagnostics.Debug.WriteLine($"清理资源时发生错误: {ex.Message}");
            }

            base.OnFormClosing(e);
        }

        /// <summary>
        /// 显示可视化结果
        /// </summary>
        private void ShowVisualizationResult()
        {
            try
            {
                if (_generator.GeneratedPanels != null && _generator.GeneratedPanels.Count > 0)
                {
                    // 获取生成的板材数据
                    var panels = _generator.GeneratedPanels.ToList();
                    var windows = new List<Rectangle2D>();
                    var doors = new List<Rectangle2D>();

                    // 创建示例数据（在实际应用中应该从_generator获取）
                    windows.Add(new Rectangle2D(1000, 800, 1500, 2200));
                    windows.Add(new Rectangle2D(3000, 800, 3500, 2200));
                    doors.Add(new Rectangle2D(2000, 0, 2800, 2100));

                    // 创建轮廓线
                    var outline = new Polygon2D();
                    outline.AddVertex(0, 0);
                    outline.AddVertex(5000, 0);
                    outline.AddVertex(5000, 3000);
                    outline.AddVertex(0, 3000);
                    outline.IsClosed = true;

                    // 显示可视化窗体
                    var visualForm = new PanelVisualizationForm(panels, windows, doors, outline);
                    visualForm.Show();
                }
            }
            catch (Exception ex)
            {
                ShowError("显示可视化结果时发生错误", ex);
            }
        }
        #endregion
    }
}
