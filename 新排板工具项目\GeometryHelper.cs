using System;
using System.Collections.Generic;
using System.Drawing;

namespace SmartPanelTool
{
    /// <summary>
    /// 几何计算辅助类
    /// </summary>
    public static class GeometryHelper
    {
        #region 常量定义
        public const double TOLERANCE = 1e-9;
        public const double MIN_PANEL_HEIGHT = 1.0;
        public const int MAX_ITERATIONS = 10000;
        public const double MIN_PANEL_WIDTH = 0.1;
        #endregion

        #region 基础几何结构
        /// <summary>
        /// 二维点结构
        /// </summary>
        public struct Point2D
        {
            public double X { get; set; }
            public double Y { get; set; }

            public Point2D(double x, double y)
            {
                X = x;
                Y = y;
            }

            public static Point2D operator +(Point2D a, Point2D b)
            {
                return new Point2D(a.X + b.X, a.Y + b.Y);
            }

            public static Point2D operator -(Point2D a, Point2D b)
            {
                return new Point2D(a.X - b.X, a.Y - b.Y);
            }

            public double DistanceTo(Point2D other)
            {
                double dx = X - other.X;
                double dy = Y - other.Y;
                return Math.Sqrt(dx * dx + dy * dy);
            }

            public override string ToString()
            {
                return $"({X:F2}, {Y:F2})";
            }
        }

        /// <summary>
        /// 矩形结构
        /// </summary>
        public struct Rectangle2D
        {
            public double MinX { get; set; }
            public double MinY { get; set; }
            public double MaxX { get; set; }
            public double MaxY { get; set; }

            public Rectangle2D(double minX, double minY, double maxX, double maxY)
            {
                MinX = Math.Min(minX, maxX);
                MinY = Math.Min(minY, maxY);
                MaxX = Math.Max(minX, maxX);
                MaxY = Math.Max(minY, maxY);
            }

            public double Width => MaxX - MinX;
            public double Height => MaxY - MinY;
            public Point2D Center => new Point2D((MinX + MaxX) / 2, (MinY + MaxY) / 2);

            /// <summary>
            /// 检查是否与另一个矩形相交
            /// </summary>
            public bool IntersectsWith(Rectangle2D other)
            {
                return !(MaxX < other.MinX || MinX > other.MaxX || MaxY < other.MinY || MinY > other.MaxY);
            }

            /// <summary>
            /// 检查是否完全包含另一个矩形
            /// </summary>
            public bool Contains(Rectangle2D other)
            {
                return MinX <= other.MinX && MinY <= other.MinY && MaxX >= other.MaxX && MaxY >= other.MaxY;
            }

            /// <summary>
            /// 检查点是否在矩形内
            /// </summary>
            public bool Contains(Point2D point)
            {
                return point.X >= MinX && point.X <= MaxX && point.Y >= MinY && point.Y <= MaxY;
            }

            public override string ToString()
            {
                return $"[({MinX:F2}, {MinY:F2}) - ({MaxX:F2}, {MaxY:F2})]";
            }
        }

        /// <summary>
        /// 多边形类
        /// </summary>
        public class Polygon2D
        {
            public List<Point2D> Vertices { get; private set; }
            public bool IsClosed { get; set; }

            public Polygon2D()
            {
                Vertices = new List<Point2D>();
                IsClosed = false;
            }

            public Polygon2D(List<Point2D> vertices, bool isClosed = false)
            {
                Vertices = new List<Point2D>(vertices);
                IsClosed = isClosed;
            }

            /// <summary>
            /// 添加顶点
            /// </summary>
            public void AddVertex(Point2D vertex)
            {
                Vertices.Add(vertex);
            }

            /// <summary>
            /// 添加顶点
            /// </summary>
            public void AddVertex(double x, double y)
            {
                Vertices.Add(new Point2D(x, y));
            }

            /// <summary>
            /// 获取边界矩形
            /// </summary>
            public Rectangle2D GetBounds()
            {
                if (Vertices.Count == 0)
                    return new Rectangle2D(0, 0, 0, 0);

                double minX = double.MaxValue;
                double minY = double.MaxValue;
                double maxX = double.MinValue;
                double maxY = double.MinValue;

                foreach (var vertex in Vertices)
                {
                    minX = Math.Min(minX, vertex.X);
                    minY = Math.Min(minY, vertex.Y);
                    maxX = Math.Max(maxX, vertex.X);
                    maxY = Math.Max(maxY, vertex.Y);
                }

                return new Rectangle2D(minX, minY, maxX, maxY);
            }

            /// <summary>
            /// 检查点是否在多边形内部（射线法）
            /// </summary>
            public bool ContainsPoint(Point2D point)
            {
                if (Vertices.Count < 3)
                    return false;

                int intersections = 0;
                int vertexCount = IsClosed ? Vertices.Count : Vertices.Count - 1;

                for (int i = 0; i < vertexCount; i++)
                {
                    Point2D p1 = Vertices[i];
                    Point2D p2 = Vertices[(i + 1) % Vertices.Count];

                    // 避免处理水平线段
                    if (Math.Abs(p1.Y - p2.Y) < TOLERANCE)
                        continue;

                    // 判断射线是否与线段相交
                    if ((p1.Y > point.Y) != (p2.Y > point.Y))
                    {
                        // 计算交点的X坐标
                        double intersectionX = (p2.X - p1.X) * (point.Y - p1.Y) / (p2.Y - p1.Y) + p1.X;

                        // 只计算在射线右侧的交点
                        if (point.X < intersectionX - TOLERANCE)
                        {
                            intersections++;
                        }
                    }
                }

                // 奇数次交叉表示点在多边形内部
                return (intersections % 2 == 1);
            }

            /// <summary>
            /// 获取指定X坐标处的Y范围
            /// </summary>
            public List<Tuple<double, double>> GetYRangesAtX(double x)
            {
                var ranges = new List<Tuple<double, double>>();
                var intersections = new List<double>();

                int vertexCount = IsClosed ? Vertices.Count : Vertices.Count - 1;

                for (int i = 0; i < vertexCount; i++)
                {
                    Point2D p1 = Vertices[i];
                    Point2D p2 = Vertices[(i + 1) % Vertices.Count];

                    // 检查线段是否与垂直线x相交
                    if (IsLineIntersectingVerticalLine(p1, p2, x, out double y))
                    {
                        intersections.Add(y);
                    }
                }

                // 对交点排序
                intersections.Sort();

                // 将交点配对形成Y范围
                for (int i = 0; i < intersections.Count - 1; i += 2)
                {
                    if (i + 1 < intersections.Count)
                    {
                        ranges.Add(new Tuple<double, double>(intersections[i], intersections[i + 1]));
                    }
                }

                return ranges;
            }

            /// <summary>
            /// 检查线段是否与垂直线相交
            /// </summary>
            private bool IsLineIntersectingVerticalLine(Point2D p1, Point2D p2, double x, out double y)
            {
                y = 0;

                // 检查X范围
                double minX = Math.Min(p1.X, p2.X);
                double maxX = Math.Max(p1.X, p2.X);

                if (x < minX - TOLERANCE || x > maxX + TOLERANCE)
                    return false;

                // 如果是垂直线段
                if (Math.Abs(p2.X - p1.X) < TOLERANCE)
                {
                    if (Math.Abs(x - p1.X) < TOLERANCE)
                    {
                        y = (p1.Y + p2.Y) / 2; // 返回中点Y坐标
                        return true;
                    }
                    return false;
                }

                // 计算交点Y坐标
                double t = (x - p1.X) / (p2.X - p1.X);
                if (t >= -TOLERANCE && t <= 1.0 + TOLERANCE)
                {
                    y = p1.Y + t * (p2.Y - p1.Y);
                    return true;
                }

                return false;
            }
        }
        #endregion

        #region 几何计算方法
        /// <summary>
        /// 检查两个数值是否在容差范围内相等
        /// </summary>
        public static bool AreEqual(double a, double b, double tolerance = TOLERANCE)
        {
            return Math.Abs(a - b) < tolerance;
        }

        /// <summary>
        /// 检查矩形是否在水平方向上完全位于另一个矩形内部
        /// </summary>
        public static bool IsRectCompletelyInsideHorizontally(double rectMinX, double rectMaxX, double containerMinX, double containerMaxX)
        {
            return rectMinX >= containerMinX - TOLERANCE && rectMaxX <= containerMaxX + TOLERANCE;
        }

        /// <summary>
        /// 检查两个X范围是否重叠
        /// </summary>
        public static bool IsOverlappingHorizontally(double rect1MinX, double rect1MaxX, double rect2MinX, double rect2MaxX)
        {
            return rect1MinX <= rect2MaxX + TOLERANCE && rect1MaxX >= rect2MinX - TOLERANCE;
        }

        /// <summary>
        /// 分割Y范围，避开指定的区域
        /// </summary>
        public static List<Tuple<double, double>> SplitYRanges(List<Tuple<double, double>> ranges, double avoidMinY, double avoidMaxY)
        {
            if (ranges == null || ranges.Count == 0)
                return new List<Tuple<double, double>>();

            // 确保avoidMinY <= avoidMaxY
            if (avoidMinY > avoidMaxY)
            {
                double temp = avoidMinY;
                avoidMinY = avoidMaxY;
                avoidMaxY = temp;
            }

            var newRanges = new List<Tuple<double, double>>();

            foreach (var range in ranges)
            {
                double rangeMin = range.Item1;
                double rangeMax = range.Item2;

                // 如果避开区域不与当前范围重叠，直接添加
                if (avoidMaxY < rangeMin - TOLERANCE || avoidMinY > rangeMax + TOLERANCE)
                {
                    newRanges.Add(range);
                    continue;
                }

                // 添加避开区域下方的部分
                if (rangeMin < avoidMinY - TOLERANCE)
                {
                    double segmentHeight = avoidMinY - rangeMin;
                    if (segmentHeight >= MIN_PANEL_HEIGHT)
                    {
                        newRanges.Add(new Tuple<double, double>(rangeMin, avoidMinY));
                    }
                }

                // 添加避开区域上方的部分
                if (rangeMax > avoidMaxY + TOLERANCE)
                {
                    double segmentHeight = rangeMax - avoidMaxY;
                    if (segmentHeight >= MIN_PANEL_HEIGHT)
                    {
                        newRanges.Add(new Tuple<double, double>(avoidMaxY, rangeMax));
                    }
                }
            }

            return newRanges;
        }
        #endregion
    }
}
