using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using static SmartRectangularPanelPlugin.GeometryUtils;

namespace SmartRectangularPanelPlugin
{
    /// <summary>
    /// 排版方向枚举
    /// </summary>
    public enum PanelDirection
    {
        /// <summary>
        /// 从左往右排版
        /// </summary>
        LeftToRight,

        /// <summary>
        /// 从右往左排版
        /// </summary>
        RightToLeft,

        /// <summary>
        /// 从中间向两边排版
        /// </summary>
        CenterToSides
    }

    /// <summary>
    /// 智能排板生成器 - 增强版，支持复杂轮廓线顶部处理
    /// </summary>
    public class SmartPanelGenerator
    {
        #region 私有字段
        private Document _doc;
        private Database _db;
        private Editor _ed;
        
        private ObjectId _outlineId;
        private List<ObjectId> _windowIds;
        private List<ObjectId> _doorIds;
        private List<ContourSegment> _contourSegments;
        
        // 排版参数
        private double _startDistance;
        private double _panelWidth;
        private double _topGap;
        private double _bottomGap;
        private double _windowGap;
        private double _doorTopGap;
        private PanelDirection _direction;
        
        // 生成结果
        private List<ObjectId> _generatedPanels;
        private int _totalPanelCount;
        private double _totalArea;
        #endregion

        #region 属性
        public bool HasOutline => !_outlineId.IsNull;
        public int WindowCount => _windowIds?.Count ?? 0;
        public int DoorCount => _doorIds?.Count ?? 0;
        public int GeneratedPanelCount => _totalPanelCount;
        public double TotalArea => _totalArea;

        // 参数属性
        public double StartDistance { get => _startDistance; set => _startDistance = value; }
        public double PanelWidth { get => _panelWidth; set => _panelWidth = Math.Max(value, MIN_PANEL_WIDTH); }
        public double TopGap { get => _topGap; set => _topGap = Math.Max(value, 0); }
        public double BottomGap { get => _bottomGap; set => _bottomGap = Math.Max(value, 0); }
        public double WindowGap { get => _windowGap; set => _windowGap = Math.Max(value, 0); }
        public double DoorTopGap { get => _doorTopGap; set => _doorTopGap = Math.Max(value, 0); }
        public PanelDirection Direction { get => _direction; set => _direction = value; }
        #endregion

        #region 构造函数
        public SmartPanelGenerator()
        {
            _doc = Application.DocumentManager.MdiActiveDocument;
            _db = _doc.Database;
            _ed = _doc.Editor;
            
            _windowIds = new List<ObjectId>();
            _doorIds = new List<ObjectId>();
            _generatedPanels = new List<ObjectId>();
            _contourSegments = new List<ContourSegment>();
            
            // 设置默认参数
            _panelWidth = 1000;
            _topGap = 5;
            _bottomGap = 10;
            _windowGap = 5;
            _doorTopGap = 5;
            _direction = PanelDirection.LeftToRight;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 选择轮廓线
        /// </summary>
        public bool SelectOutline()
        {
            try
            {
                var peo = new PromptEntityOptions("\n请选择轮廓线（多段线）: ");
                peo.SetRejectMessage("\n必须选择多段线。");
                peo.AddAllowedClass(typeof(Polyline), true);
                
                var per = _ed.GetEntity(peo);
                if (per.Status != PromptStatus.OK)
                    return false;
                
                _outlineId = per.ObjectId;
                
                // 分析轮廓线顶部特征
                using (var tr = _db.TransactionManager.StartTransaction())
                {
                    var polyline = tr.GetObject(_outlineId, OpenMode.ForRead) as Polyline;
                    if (polyline != null)
                    {
                        _contourSegments = AnalyzeContourTop(polyline);
                        _ed.WriteMessage($"\n轮廓线分析完成，识别到 {_contourSegments.Count} 个线段。");
                        
                        // 显示轮廓线顶部特征分析结果
                        DisplayContourAnalysis();
                    }
                    tr.Commit();
                }
                
                return true;
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n选择轮廓线时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 显示轮廓线分析结果
        /// </summary>
        private void DisplayContourAnalysis()
        {
            _ed.WriteMessage("\n=== 轮廓线顶部特征分析 ===");
            
            var typeCount = new Dictionary<ContourTopType, int>();
            foreach (var segment in _contourSegments)
            {
                if (!typeCount.ContainsKey(segment.TopType))
                    typeCount[segment.TopType] = 0;
                typeCount[segment.TopType]++;
            }
            
            foreach (var kvp in typeCount)
            {
                string typeName = GetTopTypeDescription(kvp.Key);
                _ed.WriteMessage($"\n{typeName}: {kvp.Value} 段");
            }
            
            _ed.WriteMessage("\n========================");
        }

        /// <summary>
        /// 获取顶部类型描述
        /// </summary>
        private string GetTopTypeDescription(ContourTopType type)
        {
            switch (type)
            {
                case ContourTopType.Horizontal: return "水平直线";
                case ContourTopType.Sloped: return "斜坡";
                case ContourTopType.Curved: return "弧形";
                case ContourTopType.Sawtooth: return "锯齿形";
                case ContourTopType.Irregular: return "不规则";
                case ContourTopType.Stepped: return "阶梯形";
                default: return "未知";
            }
        }

        /// <summary>
        /// 选择窗洞口
        /// </summary>
        public bool SelectWindows()
        {
            try
            {
                var pso = new PromptSelectionOptions();
                pso.MessageForAdding = "\n请选择窗洞口（矩形或多段线）: ";
                pso.AllowDuplicates = false;
                
                var filter = new SelectionFilter(new TypedValue[]
                {
                    new TypedValue((int)DxfCode.Start, "LWPOLYLINE,POLYLINE")
                });
                
                var psr = _ed.GetSelection(pso, filter);
                if (psr.Status != PromptStatus.OK)
                    return false;
                
                _windowIds.Clear();
                foreach (SelectedObject so in psr.Value)
                {
                    _windowIds.Add(so.ObjectId);
                }
                
                _ed.WriteMessage($"\n已选择 {_windowIds.Count} 个窗洞口。");
                return true;
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n选择窗洞口时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 选择门洞口
        /// </summary>
        public bool SelectDoors()
        {
            try
            {
                var pso = new PromptSelectionOptions();
                pso.MessageForAdding = "\n请选择门洞口（矩形或多段线）: ";
                pso.AllowDuplicates = false;
                
                var filter = new SelectionFilter(new TypedValue[]
                {
                    new TypedValue((int)DxfCode.Start, "LWPOLYLINE,POLYLINE")
                });
                
                var psr = _ed.GetSelection(pso, filter);
                if (psr.Status != PromptStatus.OK)
                    return false;
                
                _doorIds.Clear();
                foreach (SelectedObject so in psr.Value)
                {
                    _doorIds.Add(so.ObjectId);
                }
                
                _ed.WriteMessage($"\n已选择 {_doorIds.Count} 个门洞口。");
                return true;
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n选择门洞口时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成矩形板 - 主要入口方法
        /// </summary>
        public bool GeneratePanels()
        {
            if (!HasOutline)
            {
                _ed.WriteMessage("\n错误：请先选择轮廓线。");
                return false;
            }

            try
            {
                _ed.WriteMessage("\n开始生成矩形板...");
                _ed.WriteMessage($"\n排版参数：板宽={_panelWidth}, 顶部间隙={_topGap}, 底部间隙={_bottomGap}");
                _ed.WriteMessage($"\n窗户间隙={_windowGap}, 门顶部间隙={_doorTopGap}");
                
                // 清空之前的结果
                ClearPreviousResults();
                
                using (var tr = _db.TransactionManager.StartTransaction())
                {
                    // 获取轮廓线边界
                    var outline = tr.GetObject(_outlineId, OpenMode.ForRead) as Polyline;
                    if (outline == null)
                    {
                        _ed.WriteMessage("\n错误：无法获取轮廓线对象。");
                        return false;
                    }
                    
                    var bounds = outline.GeometricExtents;
                    double minX = bounds.MinPoint.X;
                    double maxX = bounds.MaxPoint.X;
                    
                    // 获取门窗洞口信息
                    var windows = GetWindowExtents(tr);
                    var doors = GetDoorExtents(tr);
                    
                    _ed.WriteMessage($"\n轮廓线范围：X({minX:F2} - {maxX:F2})");
                    _ed.WriteMessage($"\n检测到 {windows.Count} 个窗洞口，{doors.Count} 个门洞口");
                    
                    // 根据排版方向生成板材
                    bool success = false;
                    switch (_direction)
                    {
                        case PanelDirection.LeftToRight:
                            success = GenerateLeftToRightPanels(tr, minX, maxX, windows, doors);
                            break;
                        case PanelDirection.RightToLeft:
                            success = GenerateRightToLeftPanels(tr, minX, maxX, windows, doors);
                            break;
                        case PanelDirection.CenterToSides:
                            success = GenerateCenterToSidesPanels(tr, minX, maxX, windows, doors);
                            break;
                    }
                    
                    if (success)
                    {
                        tr.Commit();
                        _ed.WriteMessage($"\n生成完成！共生成 {_totalPanelCount} 个矩形板，总面积：{_totalArea:F2}");
                        return true;
                    }
                    else
                    {
                        tr.Abort();
                        _ed.WriteMessage("\n生成失败。");
                        return false;
                    }
                }
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n生成矩形板时发生错误: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region 私有生成方法
        /// <summary>
        /// 清空之前的生成结果
        /// </summary>
        private void ClearPreviousResults()
        {
            _generatedPanels.Clear();
            _totalPanelCount = 0;
            _totalArea = 0;
        }

        /// <summary>
        /// 获取窗洞口范围
        /// </summary>
        private List<Extents3d> GetWindowExtents(Transaction tr)
        {
            var extents = new List<Extents3d>();

            foreach (var windowId in _windowIds)
            {
                try
                {
                    var entity = tr.GetObject(windowId, OpenMode.ForRead) as Entity;
                    if (entity != null)
                    {
                        extents.Add(entity.GeometricExtents);
                    }
                }
                catch
                {
                    // 忽略无效的对象
                }
            }

            return extents;
        }

        /// <summary>
        /// 获取门洞口范围
        /// </summary>
        private List<Extents3d> GetDoorExtents(Transaction tr)
        {
            var extents = new List<Extents3d>();

            foreach (var doorId in _doorIds)
            {
                try
                {
                    var entity = tr.GetObject(doorId, OpenMode.ForRead) as Entity;
                    if (entity != null)
                    {
                        extents.Add(entity.GeometricExtents);
                    }
                }
                catch
                {
                    // 忽略无效的对象
                }
            }

            return extents;
        }

        /// <summary>
        /// 从左往右生成板材
        /// </summary>
        private bool GenerateLeftToRightPanels(Transaction tr, double minX, double maxX,
            List<Extents3d> windows, List<Extents3d> doors)
        {
            try
            {
                double currentX = minX + _startDistance;
                int iterationCount = 0;

                while (currentX + _panelWidth <= maxX && iterationCount < MAX_ITERATIONS)
                {
                    double panelMinX = currentX;
                    double panelMaxX = currentX + _panelWidth;

                    int panelsGenerated = GeneratePanelAtPosition(tr, panelMinX, panelMaxX, windows, doors);
                    _totalPanelCount += panelsGenerated;

                    currentX += _panelWidth;
                    iterationCount++;
                }

                return true;
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n从左往右生成时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从右往左生成板材
        /// </summary>
        private bool GenerateRightToLeftPanels(Transaction tr, double minX, double maxX,
            List<Extents3d> windows, List<Extents3d> doors)
        {
            try
            {
                double currentX = maxX - _startDistance - _panelWidth;
                int iterationCount = 0;

                while (currentX >= minX && iterationCount < MAX_ITERATIONS)
                {
                    double panelMinX = currentX;
                    double panelMaxX = currentX + _panelWidth;

                    int panelsGenerated = GeneratePanelAtPosition(tr, panelMinX, panelMaxX, windows, doors);
                    _totalPanelCount += panelsGenerated;

                    currentX -= _panelWidth;
                    iterationCount++;
                }

                return true;
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n从右往左生成时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从中间向两边生成板材
        /// </summary>
        private bool GenerateCenterToSidesPanels(Transaction tr, double minX, double maxX,
            List<Extents3d> windows, List<Extents3d> doors)
        {
            try
            {
                double centerX = (minX + maxX) / 2;
                double leftX = centerX - _panelWidth / 2;
                double rightX = centerX + _panelWidth / 2;

                // 先生成中间的板材
                if (leftX >= minX && rightX <= maxX)
                {
                    int panelsGenerated = GeneratePanelAtPosition(tr, leftX, rightX, windows, doors);
                    _totalPanelCount += panelsGenerated;
                }

                // 向左右两边扩展
                double leftCurrentX = leftX - _panelWidth;
                double rightCurrentX = rightX;
                int iterationCount = 0;

                while ((leftCurrentX >= minX || rightCurrentX + _panelWidth <= maxX) && iterationCount < MAX_ITERATIONS)
                {
                    // 向左生成
                    if (leftCurrentX >= minX)
                    {
                        int leftPanels = GeneratePanelAtPosition(tr, leftCurrentX, leftCurrentX + _panelWidth, windows, doors);
                        _totalPanelCount += leftPanels;
                        leftCurrentX -= _panelWidth;
                    }

                    // 向右生成
                    if (rightCurrentX + _panelWidth <= maxX)
                    {
                        int rightPanels = GeneratePanelAtPosition(tr, rightCurrentX, rightCurrentX + _panelWidth, windows, doors);
                        _totalPanelCount += rightPanels;
                        rightCurrentX += _panelWidth;
                    }

                    iterationCount++;
                }

                return true;
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n从中间向两边生成时发生错误: {ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 在指定位置生成板材 - 核心算法，考虑复杂轮廓线顶部
        /// </summary>
        private int GeneratePanelAtPosition(Transaction tr, double panelMinX, double panelMaxX,
            List<Extents3d> windows, List<Extents3d> doors)
        {
            try
            {
                // 获取该X范围内的有效Y范围，考虑轮廓线顶部复杂情况
                var yRanges = GetValidYRanges(_contourSegments, panelMinX, panelMaxX, _topGap, _bottomGap);

                if (yRanges.Count == 0)
                {
                    return 0; // 没有有效的Y范围
                }

                // 应用智能避障算法
                yRanges = ApplySmartObstacleAvoidance(yRanges, windows, doors,
                    panelMinX, panelMaxX, _windowGap, _doorTopGap);

                // 合并重叠的范围
                yRanges = MergeOverlappingRanges(yRanges);

                // 生成矩形板
                int panelCount = 0;
                var modelSpace = tr.GetObject(_db.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;

                foreach (var yRange in yRanges)
                {
                    double height = yRange.Item2 - yRange.Item1;
                    if (height >= MIN_PANEL_HEIGHT)
                    {
                        // 创建矩形板
                        var panel = CreateRectanglePanel(panelMinX, yRange.Item1, _panelWidth, height);

                        if (panel != null)
                        {
                            var panelId = modelSpace.AppendEntity(panel);
                            tr.AddNewlyCreatedDBObject(panel, true);

                            _generatedPanels.Add(panelId);
                            _totalArea += _panelWidth * height;
                            panelCount++;
                        }
                    }
                }

                return panelCount;
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n在位置({panelMinX:F2}, {panelMaxX:F2})生成板材时发生错误: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 创建矩形板
        /// </summary>
        private Polyline CreateRectanglePanel(double x, double y, double width, double height)
        {
            try
            {
                var panel = new Polyline();
                panel.AddVertexAt(0, new Point2d(x, y), 0, 0, 0);
                panel.AddVertexAt(1, new Point2d(x + width, y), 0, 0, 0);
                panel.AddVertexAt(2, new Point2d(x + width, y + height), 0, 0, 0);
                panel.AddVertexAt(3, new Point2d(x, y + height), 0, 0, 0);
                panel.Closed = true;

                // 设置图层和颜色
                panel.Layer = "矩形板";
                panel.ColorIndex = 3; // 绿色

                return panel;
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n创建矩形板时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 删除所有生成的板材
        /// </summary>
        public bool DeleteAllPanels()
        {
            try
            {
                using (var tr = _db.TransactionManager.StartTransaction())
                {
                    foreach (var panelId in _generatedPanels)
                    {
                        try
                        {
                            var panel = tr.GetObject(panelId, OpenMode.ForWrite);
                            panel.Erase();
                        }
                        catch
                        {
                            // 忽略已删除的对象
                        }
                    }

                    tr.Commit();
                    ClearPreviousResults();
                    _ed.WriteMessage("\n所有生成的板材已删除。");
                    return true;
                }
            }
            catch (System.Exception ex)
            {
                _ed.WriteMessage($"\n删除板材时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取生成统计信息
        /// </summary>
        public string GetStatistics()
        {
            return $"生成统计：\n" +
                   $"- 板材数量：{_totalPanelCount}\n" +
                   $"- 总面积：{_totalArea:F2}\n" +
                   $"- 轮廓线段数：{_contourSegments.Count}\n" +
                   $"- 窗洞口数：{WindowCount}\n" +
                   $"- 门洞口数：{DoorCount}";
        }
        #endregion
    }
}
