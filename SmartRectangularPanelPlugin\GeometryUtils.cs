using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.DatabaseServices;

namespace SmartRectangularPanelPlugin
{
    /// <summary>
    /// 几何工具类 - 专门处理复杂轮廓线和智能避障
    /// </summary>
    public static class GeometryUtils
    {
        #region 常量定义
        public const double TOLERANCE = 1e-9;
        public const double MIN_PANEL_HEIGHT = 1.0;
        public const double MIN_PANEL_WIDTH = 0.1;
        public const int MAX_ITERATIONS = 10000;
        public const int SAMPLING_DENSITY = 50; // 轮廓线采样密度
        #endregion

        #region 轮廓线顶部处理
        /// <summary>
        /// 轮廓线顶部类型枚举
        /// </summary>
        public enum ContourTopType
        {
            /// <summary>
            /// 水平直线顶部
            /// </summary>
            Horizontal,
            
            /// <summary>
            /// 斜坡顶部
            /// </summary>
            Sloped,
            
            /// <summary>
            /// 弧形顶部
            /// </summary>
            Curved,
            
            /// <summary>
            /// 锯齿形顶部
            /// </summary>
            Saw<PERSON>,
            
            /// <summary>
            /// 不规则复杂顶部
            /// </summary>
            Irregular,
            
            /// <summary>
            /// 阶梯形顶部
            /// </summary>
            Stepped
        }

        /// <summary>
        /// 轮廓线段信息
        /// </summary>
        public class ContourSegment
        {
            public Point3d StartPoint { get; set; }
            public Point3d EndPoint { get; set; }
            public ContourTopType TopType { get; set; }
            public double MinX => Math.Min(StartPoint.X, EndPoint.X);
            public double MaxX => Math.Max(StartPoint.X, EndPoint.X);
            public double MinY => Math.Min(StartPoint.Y, EndPoint.Y);
            public double MaxY => Math.Max(StartPoint.Y, EndPoint.Y);
            
            /// <summary>
            /// 获取指定X坐标处的Y值
            /// </summary>
            public double GetYAtX(double x)
            {
                if (Math.Abs(EndPoint.X - StartPoint.X) < TOLERANCE)
                {
                    // 垂直线段
                    return (StartPoint.Y + EndPoint.Y) / 2;
                }
                
                // 线性插值
                double t = (x - StartPoint.X) / (EndPoint.X - StartPoint.X);
                return StartPoint.Y + t * (EndPoint.Y - StartPoint.Y);
            }
        }

        /// <summary>
        /// 分析轮廓线顶部特征
        /// </summary>
        public static List<ContourSegment> AnalyzeContourTop(Polyline polyline)
        {
            var segments = new List<ContourSegment>();
            
            if (polyline == null || polyline.NumberOfVertices < 2)
                return segments;

            try
            {
                // 获取所有顶点
                var vertices = new List<Point3d>();
                for (int i = 0; i < polyline.NumberOfVertices; i++)
                {
                    vertices.Add(polyline.GetPoint3dAt(i));
                }

                // 如果是闭合多段线，确保首尾相连
                if (polyline.Closed && vertices.Count > 0)
                {
                    vertices.Add(vertices[0]);
                }

                // 分析每个线段
                for (int i = 0; i < vertices.Count - 1; i++)
                {
                    var segment = new ContourSegment
                    {
                        StartPoint = vertices[i],
                        EndPoint = vertices[i + 1],
                        TopType = DetermineSegmentTopType(vertices, i)
                    };
                    segments.Add(segment);
                }

                // 按X坐标排序
                segments.Sort((a, b) => a.MinX.CompareTo(b.MinX));
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"分析轮廓线顶部特征时发生错误: {ex.Message}", ex);
            }

            return segments;
        }

        /// <summary>
        /// 确定线段的顶部类型
        /// </summary>
        private static ContourTopType DetermineSegmentTopType(List<Point3d> vertices, int segmentIndex)
        {
            var current = vertices[segmentIndex];
            var next = vertices[segmentIndex + 1];
            
            // 计算线段的斜率
            double deltaX = next.X - current.X;
            double deltaY = next.Y - current.Y;
            
            if (Math.Abs(deltaX) < TOLERANCE)
            {
                // 垂直线段
                return ContourTopType.Irregular;
            }
            
            double slope = deltaY / deltaX;
            
            if (Math.Abs(slope) < TOLERANCE)
            {
                // 水平线段
                return ContourTopType.Horizontal;
            }
            else if (Math.Abs(slope) < 0.1)
            {
                // 近似水平，可能是轻微斜坡
                return ContourTopType.Sloped;
            }
            else
            {
                // 分析周围线段来确定更复杂的类型
                return AnalyzeComplexTopType(vertices, segmentIndex, slope);
            }
        }

        /// <summary>
        /// 分析复杂顶部类型
        /// </summary>
        private static ContourTopType AnalyzeComplexTopType(List<Point3d> vertices, int segmentIndex, double slope)
        {
            // 检查前后线段的斜率变化
            if (segmentIndex > 0 && segmentIndex < vertices.Count - 2)
            {
                var prev = vertices[segmentIndex - 1];
                var current = vertices[segmentIndex];
                var next = vertices[segmentIndex + 1];
                var nextNext = vertices[segmentIndex + 2];
                
                // 计算前一段和后一段的斜率
                double prevSlope = GetSlope(prev, current);
                double nextSlope = GetSlope(next, nextNext);
                
                // 判断是否为锯齿形
                if (Math.Sign(prevSlope) != Math.Sign(slope) && Math.Sign(slope) != Math.Sign(nextSlope))
                {
                    return ContourTopType.Sawtooth;
                }
                
                // 判断是否为阶梯形
                if (Math.Abs(prevSlope) < TOLERANCE && Math.Abs(nextSlope) < TOLERANCE && Math.Abs(slope) > 1.0)
                {
                    return ContourTopType.Stepped;
                }
            }
            
            // 根据斜率大小判断
            if (Math.Abs(slope) > 1.0)
            {
                return ContourTopType.Irregular;
            }
            else
            {
                return ContourTopType.Sloped;
            }
        }

        /// <summary>
        /// 计算两点间的斜率
        /// </summary>
        private static double GetSlope(Point3d p1, Point3d p2)
        {
            double deltaX = p2.X - p1.X;
            if (Math.Abs(deltaX) < TOLERANCE)
                return double.PositiveInfinity;
            return (p2.Y - p1.Y) / deltaX;
        }
        #endregion

        #region 智能Y范围计算
        /// <summary>
        /// 获取指定X范围内的有效Y范围，考虑轮廓线顶部复杂情况
        /// </summary>
        public static List<Tuple<double, double>> GetValidYRanges(
            List<ContourSegment> contourSegments, 
            double minX, 
            double maxX,
            double topGap = 0,
            double bottomGap = 0)
        {
            var yRanges = new List<Tuple<double, double>>();
            
            try
            {
                // 在X范围内进行高密度采样
                int sampleCount = Math.Max(SAMPLING_DENSITY, (int)Math.Ceiling((maxX - minX) * 10));
                double step = (maxX - minX) / sampleCount;
                
                var allIntersections = new List<Tuple<double, double>>(); // (x, y)
                
                for (int i = 0; i <= sampleCount; i++)
                {
                    double x = minX + i * step;
                    if (x > maxX) x = maxX;
                    
                    var intersections = GetYIntersectionsAtX(contourSegments, x);
                    foreach (var y in intersections)
                    {
                        allIntersections.Add(new Tuple<double, double>(x, y));
                    }
                }
                
                // 按Y坐标分组并合并相邻的范围
                yRanges = ProcessIntersectionsToRanges(allIntersections, topGap, bottomGap);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"计算有效Y范围时发生错误: {ex.Message}", ex);
            }
            
            return yRanges;
        }

        /// <summary>
        /// 获取指定X坐标处与轮廓线的所有交点Y坐标
        /// </summary>
        private static List<double> GetYIntersectionsAtX(List<ContourSegment> segments, double x)
        {
            var intersections = new List<double>();
            
            foreach (var segment in segments)
            {
                if (x >= segment.MinX - TOLERANCE && x <= segment.MaxX + TOLERANCE)
                {
                    // 根据顶部类型采用不同的计算方法
                    switch (segment.TopType)
                    {
                        case ContourTopType.Horizontal:
                        case ContourTopType.Sloped:
                            intersections.Add(segment.GetYAtX(x));
                            break;
                            
                        case ContourTopType.Curved:
                            // 对于弧形，需要更精确的计算
                            intersections.AddRange(GetCurvedIntersections(segment, x));
                            break;
                            
                        case ContourTopType.Sawtooth:
                        case ContourTopType.Stepped:
                        case ContourTopType.Irregular:
                            // 对于复杂形状，使用高精度采样
                            intersections.AddRange(GetComplexIntersections(segment, x));
                            break;
                    }
                }
            }
            
            // 去重并排序
            intersections = intersections.Distinct().OrderBy(y => y).ToList();
            return intersections;
        }

        /// <summary>
        /// 获取弧形线段的交点
        /// </summary>
        private static List<double> GetCurvedIntersections(ContourSegment segment, double x)
        {
            var intersections = new List<double>();
            
            // 简化处理：对于弧形，使用线性插值作为近似
            // 在实际应用中，这里应该根据具体的弧形参数进行精确计算
            intersections.Add(segment.GetYAtX(x));
            
            return intersections;
        }

        /// <summary>
        /// 获取复杂形状线段的交点
        /// </summary>
        private static List<double> GetComplexIntersections(ContourSegment segment, double x)
        {
            var intersections = new List<double>();
            
            // 对于复杂形状，使用多点采样来获得更准确的结果
            int subSamples = 5;
            double segmentWidth = segment.MaxX - segment.MinX;
            
            if (segmentWidth > TOLERANCE)
            {
                for (int i = 0; i <= subSamples; i++)
                {
                    double sampleX = segment.MinX + i * segmentWidth / subSamples;
                    if (Math.Abs(sampleX - x) < segmentWidth / subSamples)
                    {
                        intersections.Add(segment.GetYAtX(sampleX));
                    }
                }
            }
            else
            {
                intersections.Add(segment.GetYAtX(x));
            }
            
            return intersections.Distinct().ToList();
        }

        /// <summary>
        /// 将交点处理成Y范围
        /// </summary>
        private static List<Tuple<double, double>> ProcessIntersectionsToRanges(
            List<Tuple<double, double>> intersections, 
            double topGap, 
            double bottomGap)
        {
            var ranges = new List<Tuple<double, double>>();
            
            if (intersections.Count == 0)
                return ranges;
            
            // 按Y坐标排序
            var sortedY = intersections.Select(i => i.Item2).Distinct().OrderBy(y => y).ToList();
            
            // 将Y坐标配对成范围
            for (int i = 0; i < sortedY.Count - 1; i += 2)
            {
                if (i + 1 < sortedY.Count)
                {
                    double rangeBottom = sortedY[i] + bottomGap;
                    double rangeTop = sortedY[i + 1] - topGap;
                    
                    if (rangeTop - rangeBottom >= MIN_PANEL_HEIGHT)
                    {
                        ranges.Add(new Tuple<double, double>(rangeBottom, rangeTop));
                    }
                }
            }
            
            return ranges;
        }
        #endregion

        #region 智能避障算法
        /// <summary>
        /// 应用智能避障，处理门窗洞口
        /// </summary>
        public static List<Tuple<double, double>> ApplySmartObstacleAvoidance(
            List<Tuple<double, double>> yRanges,
            List<Extents3d> windows,
            List<Extents3d> doors,
            double panelMinX,
            double panelMaxX,
            double windowGap,
            double doorTopGap)
        {
            var processedRanges = new List<Tuple<double, double>>(yRanges);
            
            try
            {
                // 处理窗户避障
                foreach (var window in windows)
                {
                    if (IsHorizontallyOverlapping(panelMinX, panelMaxX, window.MinPoint.X, window.MaxPoint.X))
                    {
                        processedRanges = SplitRangesAroundObstacle(
                            processedRanges, 
                            window.MinPoint.Y - windowGap, 
                            window.MaxPoint.Y + windowGap);
                    }
                }
                
                // 处理门洞口避障
                foreach (var door in doors)
                {
                    if (IsHorizontallyOverlapping(panelMinX, panelMaxX, door.MinPoint.X, door.MaxPoint.X))
                    {
                        // 门洞口通常只在顶部加间隙
                        processedRanges = SplitRangesAroundObstacle(
                            processedRanges, 
                            door.MinPoint.Y, 
                            door.MaxPoint.Y + doorTopGap);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"应用智能避障时发生错误: {ex.Message}", ex);
            }
            
            return processedRanges;
        }

        /// <summary>
        /// 检查水平方向是否重叠
        /// </summary>
        private static bool IsHorizontallyOverlapping(double rect1MinX, double rect1MaxX, double rect2MinX, double rect2MaxX)
        {
            return rect1MinX <= rect2MaxX + TOLERANCE && rect1MaxX >= rect2MinX - TOLERANCE;
        }

        /// <summary>
        /// 在障碍物周围分割Y范围
        /// </summary>
        private static List<Tuple<double, double>> SplitRangesAroundObstacle(
            List<Tuple<double, double>> ranges, 
            double obstacleMinY, 
            double obstacleMaxY)
        {
            var newRanges = new List<Tuple<double, double>>();
            
            foreach (var range in ranges)
            {
                // 检查范围是否与障碍物重叠
                if (obstacleMaxY < range.Item1 - TOLERANCE || obstacleMinY > range.Item2 + TOLERANCE)
                {
                    // 没有重叠，直接添加
                    newRanges.Add(range);
                    continue;
                }
                
                // 添加障碍物下方的部分
                if (range.Item1 < obstacleMinY - TOLERANCE)
                {
                    double segmentHeight = obstacleMinY - range.Item1;
                    if (segmentHeight >= MIN_PANEL_HEIGHT)
                    {
                        newRanges.Add(new Tuple<double, double>(range.Item1, obstacleMinY));
                    }
                }
                
                // 添加障碍物上方的部分
                if (range.Item2 > obstacleMaxY + TOLERANCE)
                {
                    double segmentHeight = range.Item2 - obstacleMaxY;
                    if (segmentHeight >= MIN_PANEL_HEIGHT)
                    {
                        newRanges.Add(new Tuple<double, double>(obstacleMaxY, range.Item2));
                    }
                }
            }
            
            return newRanges;
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 检查两个数值是否在容差范围内相等
        /// </summary>
        public static bool AreEqual(double a, double b, double tolerance = TOLERANCE)
        {
            return Math.Abs(a - b) < tolerance;
        }

        /// <summary>
        /// 合并相邻或重叠的Y范围
        /// </summary>
        public static List<Tuple<double, double>> MergeOverlappingRanges(List<Tuple<double, double>> ranges)
        {
            if (ranges.Count <= 1)
                return new List<Tuple<double, double>>(ranges);
            
            var sorted = ranges.OrderBy(r => r.Item1).ToList();
            var merged = new List<Tuple<double, double>>();
            var current = sorted[0];
            
            for (int i = 1; i < sorted.Count; i++)
            {
                var next = sorted[i];
                
                if (next.Item1 <= current.Item2 + TOLERANCE)
                {
                    // 合并重叠的范围
                    current = new Tuple<double, double>(current.Item1, Math.Max(current.Item2, next.Item2));
                }
                else
                {
                    merged.Add(current);
                    current = next;
                }
            }
            
            merged.Add(current);
            return merged;
        }
        #endregion
    }
}
