using System;
using System.Windows.Forms;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.EditorInput;

namespace SmartRectangularPanelPlugin
{
    /// <summary>
    /// AutoCAD命令类 - 智能矩形板排版插件
    /// </summary>
    public class Commands
    {
        private static SmartPanelForm _form = null;

        /// <summary>
        /// 智能排板命令 - 主要入口
        /// </summary>
        [CommandMethod("SMARTPANEL", CommandFlags.Modal)]
        public static void SmartPanelCommand()
        {
            try
            {
                var doc = Application.DocumentManager.MdiActiveDocument;
                var ed = doc.Editor;

                ed.WriteMessage("\n=== 智能矩形板排版插件 v2.0 ===");
                ed.WriteMessage("\n支持复杂轮廓线顶部处理和智能避障功能");
                ed.WriteMessage("\n作者：AutoCAD插件开发团队");
                ed.WriteMessage("\n=====================================");

                // 如果窗体已经存在，则显示它
                if (_form != null && !_form.IsDisposed)
                {
                    _form.Show();
                    _form.BringToFront();
                    return;
                }

                // 创建新的窗体
                _form = new SmartPanelForm();
                _form.Show();
            }
            catch (System.Exception ex)
            {
                var ed = Application.DocumentManager.MdiActiveDocument.Editor;
                ed.WriteMessage($"\n启动智能排板插件时发生错误: {ex.Message}");
                MessageBox.Show($"启动插件时发生错误:\n{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 快速排板命令 - 使用默认参数
        /// </summary>
        [CommandMethod("QUICKPANEL", CommandFlags.Modal)]
        public static void QuickPanelCommand()
        {
            try
            {
                var doc = Application.DocumentManager.MdiActiveDocument;
                var ed = doc.Editor;

                ed.WriteMessage("\n=== 快速排板模式 ===");
                ed.WriteMessage("\n使用默认参数进行快速排板");

                var generator = new SmartPanelGenerator();

                // 选择轮廓线
                if (!generator.SelectOutline())
                {
                    ed.WriteMessage("\n快速排板已取消。");
                    return;
                }

                // 询问是否选择门窗洞口
                var pko = new PromptKeywordOptions("\n是否选择门窗洞口？ [是(Y)/否(N)]");
                pko.Keywords.Add("Y");
                pko.Keywords.Add("N");
                pko.Keywords.Default = "N";
                
                var pkr = ed.GetKeywords(pko);
                if (pkr.Status == PromptStatus.OK && pkr.StringResult == "Y")
                {
                    ed.WriteMessage("\n请选择窗洞口（如果没有请直接按回车）：");
                    generator.SelectWindows();
                    
                    ed.WriteMessage("\n请选择门洞口（如果没有请直接按回车）：");
                    generator.SelectDoors();
                }

                // 使用默认参数生成
                ed.WriteMessage("\n开始使用默认参数生成板材...");
                ed.WriteMessage("\n默认参数：板宽=1000, 顶部间隙=5, 底部间隙=10");
                ed.WriteMessage("\n窗户间隙=5, 门顶部间隙=5, 排版方向=从左往右");

                bool success = generator.GeneratePanels();
                
                if (success)
                {
                    ed.WriteMessage("\n快速排板完成！");
                    ed.WriteMessage($"\n{generator.GetStatistics()}");
                }
                else
                {
                    ed.WriteMessage("\n快速排板失败。");
                }
            }
            catch (System.Exception ex)
            {
                var ed = Application.DocumentManager.MdiActiveDocument.Editor;
                ed.WriteMessage($"\n快速排板时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示插件帮助信息
        /// </summary>
        [CommandMethod("SMARTPANELHELP", CommandFlags.Modal)]
        public static void SmartPanelHelpCommand()
        {
            try
            {
                var ed = Application.DocumentManager.MdiActiveDocument.Editor;
                
                ed.WriteMessage("\n");
                ed.WriteMessage("\n=== 智能矩形板排版插件帮助 ===");
                ed.WriteMessage("\n");
                ed.WriteMessage("\n【主要功能】");
                ed.WriteMessage("\n• 智能分析轮廓线顶部特征（水平、斜坡、弧形、锯齿、阶梯、不规则）");
                ed.WriteMessage("\n• 智能避障算法，自动处理门窗洞口");
                ed.WriteMessage("\n• 支持三种排版方向：从左往右、从右往左、从中间向两边");
                ed.WriteMessage("\n• 参数化设置，支持自定义板宽、间隙等参数");
                ed.WriteMessage("\n• 实时统计生成结果");
                ed.WriteMessage("\n");
                ed.WriteMessage("\n【命令说明】");
                ed.WriteMessage("\n• SMARTPANEL  - 打开智能排板参数设置窗体");
                ed.WriteMessage("\n• QUICKPANEL  - 快速排板（使用默认参数）");
                ed.WriteMessage("\n• SMARTPANELHELP - 显示此帮助信息");
                ed.WriteMessage("\n");
                ed.WriteMessage("\n【使用步骤】");
                ed.WriteMessage("\n1. 绘制轮廓线（多段线）");
                ed.WriteMessage("\n2. 绘制门窗洞口（可选）");
                ed.WriteMessage("\n3. 运行 SMARTPANEL 命令");
                ed.WriteMessage("\n4. 选择轮廓线和门窗洞口");
                ed.WriteMessage("\n5. 设置排版参数");
                ed.WriteMessage("\n6. 点击生成板材");
                ed.WriteMessage("\n");
                ed.WriteMessage("\n【轮廓线顶部类型】");
                ed.WriteMessage("\n• 水平直线：标准的水平顶部");
                ed.WriteMessage("\n• 斜坡：倾斜的顶部");
                ed.WriteMessage("\n• 弧形：弯曲的顶部");
                ed.WriteMessage("\n• 锯齿形：锯齿状的顶部");
                ed.WriteMessage("\n• 阶梯形：阶梯状的顶部");
                ed.WriteMessage("\n• 不规则：复杂不规则的顶部");
                ed.WriteMessage("\n");
                ed.WriteMessage("\n【技术特点】");
                ed.WriteMessage("\n• 高精度几何计算（容差：1e-9）");
                ed.WriteMessage("\n• 智能Y轴分割算法");
                ed.WriteMessage("\n• 水平重叠检测");
                ed.WriteMessage("\n• 自动范围合并");
                ed.WriteMessage("\n• 性能优化（最大迭代次数保护）");
                ed.WriteMessage("\n");
                ed.WriteMessage("\n版本：v2.0");
                ed.WriteMessage("\n支持AutoCAD 2023+");
                ed.WriteMessage("\n=====================================");
            }
            catch (System.Exception ex)
            {
                var ed = Application.DocumentManager.MdiActiveDocument.Editor;
                ed.WriteMessage($"\n显示帮助信息时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 插件初始化
        /// </summary>
        [CommandMethod("SMARTPANELINIT", CommandFlags.Modal)]
        public static void InitializePlugin()
        {
            try
            {
                var ed = Application.DocumentManager.MdiActiveDocument.Editor;
                
                ed.WriteMessage("\n智能矩形板排版插件已加载！");
                ed.WriteMessage("\n输入 SMARTPANEL 开始使用");
                ed.WriteMessage("\n输入 QUICKPANEL 进行快速排板");
                ed.WriteMessage("\n输入 SMARTPANELHELP 查看帮助");
            }
            catch (System.Exception ex)
            {
                var ed = Application.DocumentManager.MdiActiveDocument.Editor;
                ed.WriteMessage($"\n初始化插件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                if (_form != null && !_form.IsDisposed)
                {
                    _form.Close();
                    _form.Dispose();
                    _form = null;
                }
            }
            catch
            {
                // 忽略清理时的错误
            }
        }
    }

    /// <summary>
    /// 插件应用程序类
    /// </summary>
    public class PluginApplication : IExtensionApplication
    {
        public void Initialize()
        {
            try
            {
                var ed = Application.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage("\n智能矩形板排版插件 v2.0 已加载");
                ed?.WriteMessage("\n支持复杂轮廓线顶部处理和智能避障功能");
            }
            catch
            {
                // 忽略初始化时的错误
            }
        }

        public void Terminate()
        {
            try
            {
                Commands.Cleanup();
            }
            catch
            {
                // 忽略终止时的错误
            }
        }
    }
}
