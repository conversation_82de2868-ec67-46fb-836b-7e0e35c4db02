using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using static SmartPanelTool.GeometryHelper;

namespace SmartPanelTool
{
    /// <summary>
    /// 板材可视化窗体
    /// </summary>
    public partial class PanelVisualizationForm : Form
    {
        #region 私有字段
        private List<Rectangle2D> _panels;
        private List<Rectangle2D> _windows;
        private List<Rectangle2D> _doors;
        private Polygon2D _outline;
        private Rectangle2D _bounds;
        private double _scale;
        private Point _offset;
        #endregion

        #region 构造函数
        public PanelVisualizationForm()
        {
            InitializeComponent();
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
        }

        public PanelVisualizationForm(List<Rectangle2D> panels, List<Rectangle2D> windows, List<Rectangle2D> doors, Polygon2D outline) : this()
        {
            _panels = panels ?? new List<Rectangle2D>();
            _windows = windows ?? new List<Rectangle2D>();
            _doors = doors ?? new List<Rectangle2D>();
            _outline = outline;

            CalculateBounds();
            CalculateScale();
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 计算边界
        /// </summary>
        private void CalculateBounds()
        {
            if (_outline != null)
            {
                _bounds = _outline.GetBounds();
            }
            else if (_panels.Count > 0)
            {
                double minX = _panels.Min(p => p.MinX);
                double minY = _panels.Min(p => p.MinY);
                double maxX = _panels.Max(p => p.MaxX);
                double maxY = _panels.Max(p => p.MaxY);
                _bounds = new Rectangle2D(minX, minY, maxX, maxY);
            }
            else
            {
                _bounds = new Rectangle2D(0, 0, 1000, 1000);
            }
        }

        /// <summary>
        /// 计算缩放比例
        /// </summary>
        private void CalculateScale()
        {
            if (ClientSize.Width == 0 || ClientSize.Height == 0)
                return;

            double scaleX = (ClientSize.Width - 40) / _bounds.Width;
            double scaleY = (ClientSize.Height - 40) / _bounds.Height;
            _scale = Math.Min(scaleX, scaleY);

            // 计算偏移量以居中显示
            double scaledWidth = _bounds.Width * _scale;
            double scaledHeight = _bounds.Height * _scale;
            _offset = new Point(
                (int)((ClientSize.Width - scaledWidth) / 2),
                (int)((ClientSize.Height - scaledHeight) / 2)
            );
        }

        /// <summary>
        /// 将世界坐标转换为屏幕坐标
        /// </summary>
        private Point WorldToScreen(Point2D worldPoint)
        {
            int screenX = (int)((worldPoint.X - _bounds.MinX) * _scale) + _offset.X;
            int screenY = (int)((worldPoint.Y - _bounds.MinY) * _scale) + _offset.Y;
            
            // 翻转Y轴（屏幕坐标系Y轴向下，世界坐标系Y轴向上）
            screenY = ClientSize.Height - screenY;
            
            return new Point(screenX, screenY);
        }

        /// <summary>
        /// 将世界矩形转换为屏幕矩形
        /// </summary>
        private Rectangle WorldToScreen(Rectangle2D worldRect)
        {
            Point topLeft = WorldToScreen(new Point2D(worldRect.MinX, worldRect.MaxY));
            Point bottomRight = WorldToScreen(new Point2D(worldRect.MaxX, worldRect.MinY));
            
            return new Rectangle(
                topLeft.X,
                topLeft.Y,
                bottomRight.X - topLeft.X,
                bottomRight.Y - topLeft.Y
            );
        }
        #endregion

        #region 事件处理
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            Graphics g = e.Graphics;
            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

            // 绘制背景
            g.Clear(Color.White);

            try
            {
                // 绘制轮廓线
                if (_outline != null && _outline.Vertices.Count > 0)
                {
                    using (var pen = new Pen(Color.Black, 2))
                    {
                        var points = _outline.Vertices.Select(v => WorldToScreen(v)).ToArray();
                        if (points.Length > 1)
                        {
                            if (_outline.IsClosed && points.Length > 2)
                            {
                                g.DrawPolygon(pen, points);
                            }
                            else
                            {
                                g.DrawLines(pen, points);
                            }
                        }
                    }
                }

                // 绘制窗洞口
                using (var brush = new SolidBrush(Color.LightBlue))
                using (var pen = new Pen(Color.Blue, 1))
                {
                    foreach (var window in _windows)
                    {
                        Rectangle rect = WorldToScreen(window);
                        g.FillRectangle(brush, rect);
                        g.DrawRectangle(pen, rect);
                    }
                }

                // 绘制门洞口
                using (var brush = new SolidBrush(Color.LightGreen))
                using (var pen = new Pen(Color.Green, 1))
                {
                    foreach (var door in _doors)
                    {
                        Rectangle rect = WorldToScreen(door);
                        g.FillRectangle(brush, rect);
                        g.DrawRectangle(pen, rect);
                    }
                }

                // 绘制生成的板材
                using (var brush = new SolidBrush(Color.FromArgb(100, Color.Red)))
                using (var pen = new Pen(Color.Red, 1))
                {
                    foreach (var panel in _panels)
                    {
                        Rectangle rect = WorldToScreen(panel);
                        g.FillRectangle(brush, rect);
                        g.DrawRectangle(pen, rect);
                    }
                }

                // 绘制图例
                DrawLegend(g);

                // 绘制统计信息
                DrawStatistics(g);
            }
            catch (Exception ex)
            {
                // 绘制错误信息
                using (var brush = new SolidBrush(Color.Red))
                using (var font = new Font("宋体", 12))
                {
                    g.DrawString($"绘制错误: {ex.Message}", font, brush, 10, 10);
                }
            }
        }

        /// <summary>
        /// 绘制图例
        /// </summary>
        private void DrawLegend(Graphics g)
        {
            int legendX = 10;
            int legendY = 10;
            int itemHeight = 20;

            using (var font = new Font("宋体", 9))
            using (var textBrush = new SolidBrush(Color.Black))
            {
                // 轮廓线
                using (var pen = new Pen(Color.Black, 2))
                {
                    g.DrawLine(pen, legendX, legendY + 5, legendX + 15, legendY + 5);
                    g.DrawString("轮廓线", font, textBrush, legendX + 20, legendY);
                }

                // 窗洞口
                legendY += itemHeight;
                using (var brush = new SolidBrush(Color.LightBlue))
                using (var pen = new Pen(Color.Blue, 1))
                {
                    g.FillRectangle(brush, legendX, legendY, 15, 10);
                    g.DrawRectangle(pen, legendX, legendY, 15, 10);
                    g.DrawString("窗洞口", font, textBrush, legendX + 20, legendY);
                }

                // 门洞口
                legendY += itemHeight;
                using (var brush = new SolidBrush(Color.LightGreen))
                using (var pen = new Pen(Color.Green, 1))
                {
                    g.FillRectangle(brush, legendX, legendY, 15, 10);
                    g.DrawRectangle(pen, legendX, legendY, 15, 10);
                    g.DrawString("门洞口", font, textBrush, legendX + 20, legendY);
                }

                // 生成的板材
                legendY += itemHeight;
                using (var brush = new SolidBrush(Color.FromArgb(100, Color.Red)))
                using (var pen = new Pen(Color.Red, 1))
                {
                    g.FillRectangle(brush, legendX, legendY, 15, 10);
                    g.DrawRectangle(pen, legendX, legendY, 15, 10);
                    g.DrawString("生成板材", font, textBrush, legendX + 20, legendY);
                }
            }
        }

        /// <summary>
        /// 绘制统计信息
        /// </summary>
        private void DrawStatistics(Graphics g)
        {
            int statsX = ClientSize.Width - 150;
            int statsY = 10;
            int itemHeight = 20;

            using (var font = new Font("宋体", 9))
            using (var textBrush = new SolidBrush(Color.Black))
            {
                g.DrawString($"板材数量: {_panels.Count}", font, textBrush, statsX, statsY);
                statsY += itemHeight;
                g.DrawString($"窗洞口: {_windows.Count}", font, textBrush, statsX, statsY);
                statsY += itemHeight;
                g.DrawString($"门洞口: {_doors.Count}", font, textBrush, statsX, statsY);

                if (_panels.Count > 0)
                {
                    double totalArea = _panels.Sum(p => p.Width * p.Height);
                    statsY += itemHeight;
                    g.DrawString($"总面积: {totalArea:F2}", font, textBrush, statsX, statsY);
                }
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            CalculateScale();
            Invalidate();
        }
        #endregion
    }
}
