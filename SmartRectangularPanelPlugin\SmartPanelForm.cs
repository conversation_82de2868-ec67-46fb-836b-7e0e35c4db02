using System;
using System.Globalization;
using System.Windows.Forms;
using Autodesk.AutoCAD.ApplicationServices;

namespace SmartRectangularPanelPlugin
{
    /// <summary>
    /// 智能排板参数设置窗体
    /// </summary>
    public partial class SmartPanelForm : Form
    {
        #region 私有字段
        private SmartPanelGenerator _generator;
        private bool _isInitializing = true;
        #endregion

        #region 构造函数
        public SmartPanelForm()
        {
            InitializeComponent();
            _generator = new SmartPanelGenerator();
            InitializeForm();
        }
        #endregion

        #region 初始化
        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                _isInitializing = true;
                
                // 设置默认值
                txtStartDistance.Text = "0";
                txtPanelWidth.Text = "1000";
                txtTopGap.Text = "5";
                txtBottomGap.Text = "10";
                txtWindowGap.Text = "5";
                txtDoorTopGap.Text = "5";
                
                // 设置默认排版方向
                rbLeftToRight.Checked = true;
                
                // 更新状态
                UpdateStatus();
                
                _isInitializing = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化窗体时发生错误: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatus()
        {
            try
            {
                lblOutlineStatus.Text = _generator.HasOutline ? "已选择" : "未选择";
                lblOutlineStatus.ForeColor = _generator.HasOutline ? System.Drawing.Color.Green : System.Drawing.Color.Red;
                
                lblWindowStatus.Text = $"{_generator.WindowCount} 个";
                lblDoorStatus.Text = $"{_generator.DoorCount} 个";
                
                // 更新生成按钮状态
                btnGenerate.Enabled = _generator.HasOutline;
                
                // 更新统计信息
                if (_generator.GeneratedPanelCount > 0)
                {
                    lblStatistics.Text = _generator.GetStatistics();
                }
                else
                {
                    lblStatistics.Text = "尚未生成板材";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新状态时发生错误: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region 事件处理
        /// <summary>
        /// 选择轮廓线按钮点击事件
        /// </summary>
        private void btnSelectOutline_Click(object sender, EventArgs e)
        {
            try
            {
                // 隐藏窗体以便用户选择
                this.Hide();
                
                bool success = _generator.SelectOutline();
                
                // 显示窗体
                this.Show();
                this.BringToFront();
                
                if (success)
                {
                    UpdateStatus();
                    MessageBox.Show("轮廓线选择成功！", "成功", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("轮廓线选择失败或被取消。", "提示", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                this.Show();
                MessageBox.Show($"选择轮廓线时发生错误: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 选择窗洞口按钮点击事件
        /// </summary>
        private void btnSelectWindows_Click(object sender, EventArgs e)
        {
            try
            {
                this.Hide();
                bool success = _generator.SelectWindows();
                this.Show();
                this.BringToFront();
                
                UpdateStatus();
                
                if (success)
                {
                    MessageBox.Show($"成功选择 {_generator.WindowCount} 个窗洞口。", "成功", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                this.Show();
                MessageBox.Show($"选择窗洞口时发生错误: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 选择门洞口按钮点击事件
        /// </summary>
        private void btnSelectDoors_Click(object sender, EventArgs e)
        {
            try
            {
                this.Hide();
                bool success = _generator.SelectDoors();
                this.Show();
                this.BringToFront();
                
                UpdateStatus();
                
                if (success)
                {
                    MessageBox.Show($"成功选择 {_generator.DoorCount} 个门洞口。", "成功", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                this.Show();
                MessageBox.Show($"选择门洞口时发生错误: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 生成板材按钮点击事件
        /// </summary>
        private void btnGenerate_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateAndApplyParameters())
                {
                    return;
                }
                
                this.Hide();
                
                bool success = _generator.GeneratePanels();
                
                this.Show();
                this.BringToFront();
                
                if (success)
                {
                    UpdateStatus();
                    MessageBox.Show("板材生成成功！", "成功", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("板材生成失败。", "错误", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                this.Show();
                MessageBox.Show($"生成板材时发生错误: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除板材按钮点击事件
        /// </summary>
        private void btnDeletePanels_Click(object sender, EventArgs e)
        {
            try
            {
                if (_generator.GeneratedPanelCount == 0)
                {
                    MessageBox.Show("没有可删除的板材。", "提示", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                
                var result = MessageBox.Show($"确定要删除所有 {_generator.GeneratedPanelCount} 个生成的板材吗？", 
                    "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    bool success = _generator.DeleteAllPanels();
                    if (success)
                    {
                        UpdateStatus();
                        MessageBox.Show("所有板材已删除。", "成功", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除板材时发生错误: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 参数文本框值改变事件
        /// </summary>
        private void ParameterTextBox_TextChanged(object sender, EventArgs e)
        {
            if (!_isInitializing)
            {
                ValidateAndApplyParameters();
            }
        }

        /// <summary>
        /// 排版方向单选按钮改变事件
        /// </summary>
        private void DirectionRadioButton_CheckedChanged(object sender, EventArgs e)
        {
            if (!_isInitializing)
            {
                ApplyDirection();
            }
        }
        #endregion

        #region 参数验证和应用
        /// <summary>
        /// 验证并应用参数
        /// </summary>
        private bool ValidateAndApplyParameters()
        {
            try
            {
                // 验证起始距离
                if (!double.TryParse(txtStartDistance.Text, NumberStyles.Float,
                    CultureInfo.InvariantCulture, out double startDistance) || startDistance < 0)
                {
                    MessageBox.Show("起始距离必须是非负数。", "参数错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtStartDistance.Focus();
                    return false;
                }

                // 验证板宽
                if (!double.TryParse(txtPanelWidth.Text, NumberStyles.Float,
                    CultureInfo.InvariantCulture, out double panelWidth) || panelWidth <= 0)
                {
                    MessageBox.Show("板宽必须是正数。", "参数错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPanelWidth.Focus();
                    return false;
                }

                // 验证顶部间隙
                if (!double.TryParse(txtTopGap.Text, NumberStyles.Float,
                    CultureInfo.InvariantCulture, out double topGap) || topGap < 0)
                {
                    MessageBox.Show("顶部间隙必须是非负数。", "参数错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtTopGap.Focus();
                    return false;
                }

                // 验证底部间隙
                if (!double.TryParse(txtBottomGap.Text, NumberStyles.Float,
                    CultureInfo.InvariantCulture, out double bottomGap) || bottomGap < 0)
                {
                    MessageBox.Show("底部间隙必须是非负数。", "参数错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtBottomGap.Focus();
                    return false;
                }

                // 验证窗户间隙
                if (!double.TryParse(txtWindowGap.Text, NumberStyles.Float,
                    CultureInfo.InvariantCulture, out double windowGap) || windowGap < 0)
                {
                    MessageBox.Show("窗户间隙必须是非负数。", "参数错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtWindowGap.Focus();
                    return false;
                }

                // 验证门顶部间隙
                if (!double.TryParse(txtDoorTopGap.Text, NumberStyles.Float,
                    CultureInfo.InvariantCulture, out double doorTopGap) || doorTopGap < 0)
                {
                    MessageBox.Show("门顶部间隙必须是非负数。", "参数错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtDoorTopGap.Focus();
                    return false;
                }

                // 应用参数到生成器
                _generator.StartDistance = startDistance;
                _generator.PanelWidth = panelWidth;
                _generator.TopGap = topGap;
                _generator.BottomGap = bottomGap;
                _generator.WindowGap = windowGap;
                _generator.DoorTopGap = doorTopGap;

                // 应用排版方向
                ApplyDirection();

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"验证参数时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 应用排版方向
        /// </summary>
        private void ApplyDirection()
        {
            try
            {
                if (rbLeftToRight.Checked)
                {
                    _generator.Direction = PanelDirection.LeftToRight;
                }
                else if (rbRightToLeft.Checked)
                {
                    _generator.Direction = PanelDirection.RightToLeft;
                }
                else if (rbCenterToSides.Checked)
                {
                    _generator.Direction = PanelDirection.CenterToSides;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用排版方向时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置参数到默认值
        /// </summary>
        private void ResetToDefaults()
        {
            try
            {
                _isInitializing = true;

                txtStartDistance.Text = "0";
                txtPanelWidth.Text = "1000";
                txtTopGap.Text = "5";
                txtBottomGap.Text = "10";
                txtWindowGap.Text = "5";
                txtDoorTopGap.Text = "5";

                rbLeftToRight.Checked = true;

                _isInitializing = false;

                ValidateAndApplyParameters();
                UpdateStatus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置参数时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion
    }
}
