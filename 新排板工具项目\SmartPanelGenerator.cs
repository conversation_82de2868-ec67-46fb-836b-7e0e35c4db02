using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using static SmartPanelTool.GeometryHelper;

namespace SmartPanelTool
{
    /// <summary>
    /// 参数验证异常
    /// </summary>
    public class InvalidParameterException : Exception
    {
        public InvalidParameterException(string message) : base(message) { }
        public InvalidParameterException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// 几何计算异常
    /// </summary>
    public class GeometryCalculationException : Exception
    {
        public GeometryCalculationException(string message) : base(message) { }
        public GeometryCalculationException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// 智能排板生成器 - 重新实现的核心算法
    /// </summary>
    public class SmartPanelGenerator
    {
        #region 私有字段
        private Polygon2D _outline;
        private List<Rectangle2D> _windows;
        private List<Rectangle2D> _doors;
        private double _startDistance;
        private double _panelWidth;
        private double _topGap;
        private double _bottomGap;
        private double _windowGap;
        private double _doorTopGap;
        private PanelDirection _direction;
        private List<Rectangle2D> _generatedPanels;
        #endregion

        #region 属性
        public bool HasOutline => _outline != null && _outline.Vertices.Count > 0;
        public int WindowCount => _windows?.Count ?? 0;
        public int DoorCount => _doors?.Count ?? 0;

        public double StartDistance
        {
            get => _startDistance;
            set => _startDistance = value;
        }

        public double PanelWidth
        {
            get => _panelWidth;
            set
            {
                if (value <= 0)
                    throw new InvalidParameterException("板宽必须大于0");
                _panelWidth = value;
            }
        }

        public double TopGap
        {
            get => _topGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("顶部间隙不能为负数");
                _topGap = value;
            }
        }

        public double BottomGap
        {
            get => _bottomGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("底部间隙不能为负数");
                _bottomGap = value;
            }
        }

        public double WindowGap
        {
            get => _windowGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("窗户间隙不能为负数");
                _windowGap = value;
            }
        }

        public double DoorTopGap
        {
            get => _doorTopGap;
            set
            {
                if (value < 0)
                    throw new InvalidParameterException("门顶部间隙不能为负数");
                _doorTopGap = value;
            }
        }

        public PanelDirection Direction { get => _direction; set => _direction = value; }

        /// <summary>
        /// 获取生成的板材列表（只读）
        /// </summary>
        public IReadOnlyList<Rectangle2D> GeneratedPanels => _generatedPanels?.AsReadOnly();
        #endregion

        #region 构造函数
        public SmartPanelGenerator()
        {
            _windows = new List<Rectangle2D>();
            _doors = new List<Rectangle2D>();
            _generatedPanels = new List<Rectangle2D>();

            // 设置默认值
            _panelWidth = 1000;
            _topGap = 5;
            _bottomGap = 10;
            _windowGap = 5;
            _doorTopGap = 5;
            _direction = PanelDirection.LeftToRight;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 设置轮廓线 - 模拟用户选择
        /// </summary>
        public bool SetOutline()
        {
            try
            {
                // 创建一个示例轮廓线用于演示
                // 在实际应用中，这里应该是用户选择的轮廓线
                var vertices = new List<Point2D>
                {
                    new Point2D(0, 0),
                    new Point2D(5000, 0),
                    new Point2D(5000, 3000),
                    new Point2D(0, 3000)
                };

                _outline = new Polygon2D(vertices, true);
                return true;
            }
            catch (Exception ex)
            {
                throw new GeometryCalculationException("设置轮廓线时发生错误", ex);
            }
        }

        /// <summary>
        /// 添加窗洞口 - 模拟用户选择
        /// </summary>
        public bool AddWindows()
        {
            try
            {
                // 创建示例窗洞口
                _windows.Add(new Rectangle2D(1000, 800, 1500, 2200));
                _windows.Add(new Rectangle2D(3000, 800, 3500, 2200));
                return true;
            }
            catch (Exception ex)
            {
                throw new GeometryCalculationException("添加窗洞口时发生错误", ex);
            }
        }

        /// <summary>
        /// 添加门洞口 - 模拟用户选择
        /// </summary>
        public bool AddDoors()
        {
            try
            {
                // 创建示例门洞口
                _doors.Add(new Rectangle2D(2000, 0, 2800, 2100));
                return true;
            }
            catch (Exception ex)
            {
                throw new GeometryCalculationException("添加门洞口时发生错误", ex);
            }
        }

        /// <summary>
        /// 清理选择
        /// </summary>
        public void ClearSelection()
        {
            _outline = null;
            _windows?.Clear();
            _doors?.Clear();
            _generatedPanels?.Clear();
        }

        /// <summary>
        /// 生成矩形板 - 核心算法入口
        /// </summary>
        public int GeneratePanels()
        {
            ValidateParameters();

            if (!HasOutline)
            {
                throw new InvalidParameterException("请先选择轮廓线");
            }

            // 清空之前生成的板材
            _generatedPanels.Clear();

            try
            {
                // 获取轮廓线边界
                var bounds = _outline.GetBounds();
                double minX = bounds.MinX;
                double maxX = bounds.MaxX;

                // 对于中心向两边排版，需要获取中心点
                Point2D? centerPoint = null;
                if (_direction == PanelDirection.CenterToSides)
                {
                    centerPoint = GetCenterPoint();
                    if (!centerPoint.HasValue)
                    {
                        return 0; // 用户取消了中心点选择
                    }
                }

                // 根据不同排版方向生成板子
                switch (_direction)
                {
                    case PanelDirection.LeftToRight:
                        return GenerateLeftToRightPanels(minX, maxX);
                    case PanelDirection.RightToLeft:
                        return GenerateRightToLeftPanels(minX, maxX);
                    case PanelDirection.CenterToSides:
                        if (centerPoint.HasValue)
                        {
                            return GenerateCenterToSidesPanels(minX, maxX, centerPoint.Value.X);
                        }
                        break;
                }

                return 0;
            }
            catch (Exception ex)
            {
                throw new GeometryCalculationException("生成矩形板时发生错误", ex);
            }
        }

        /// <summary>
        /// 获取中心点 - 模拟用户点击
        /// </summary>
        private Point2D? GetCenterPoint()
        {
            // 在实际应用中，这里应该是用户交互选择中心点
            // 现在返回轮廓线的中心点作为示例
            var bounds = _outline.GetBounds();
            return bounds.Center;
        }
        #endregion

        #region 私有方法 - 参数验证
        /// <summary>
        /// 验证参数
        /// </summary>
        private void ValidateParameters()
        {
            if (_panelWidth <= MIN_PANEL_WIDTH)
                throw new InvalidParameterException($"板宽必须大于{MIN_PANEL_WIDTH}");

            if (_topGap < 0)
                throw new InvalidParameterException("顶部间隙不能为负数");

            if (_bottomGap < 0)
                throw new InvalidParameterException("底部间隙不能为负数");

            if (_windowGap < 0)
                throw new InvalidParameterException("窗户间隙不能为负数");

            if (_doorTopGap < 0)
                throw new InvalidParameterException("门顶部间隙不能为负数");
        }
        #endregion

        #region 私有方法 - 排板算法实现
        /// <summary>
        /// 从左往右排版
        /// </summary>
        private int GenerateLeftToRightPanels(double minX, double maxX)
        {
            int panelCount = 0;
            double currentX = minX + _startDistance;
            double endX = maxX;
            int iterations = 0;

            // 开始排版直到填满整个轮廓，添加循环保护
            while ((currentX < endX || (_startDistance < 0 && currentX < minX)) && iterations < MAX_ITERATIONS)
            {
                double nextX = currentX + _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(currentX, nextX);

                // 更新当前X位置
                currentX = nextX;
                iterations++;

                // 如果起始距离为正且当前位置已超出结束点，则停止排版
                if (currentX >= endX && _startDistance >= 0)
                {
                    break;
                }
            }

            if (iterations >= MAX_ITERATIONS)
            {
                MessageBox.Show($"警告：达到最大迭代次数{MAX_ITERATIONS}，排版可能不完整", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            return panelCount;
        }

        /// <summary>
        /// 从右往左排版
        /// </summary>
        private int GenerateRightToLeftPanels(double minX, double maxX)
        {
            int panelCount = 0;
            double currentX = maxX - _startDistance;
            double endX = minX;
            int iterations = 0;

            // 开始排版直到填满整个轮廓，添加循环保护
            while ((currentX > endX || (_startDistance < 0 && currentX > maxX)) && iterations < MAX_ITERATIONS)
            {
                double nextX = currentX - _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(currentX, nextX);

                // 更新当前X位置
                currentX = nextX;
                iterations++;

                // 如果起始距离为正且当前位置已超出结束点，则停止排版
                if (currentX <= endX && _startDistance >= 0)
                {
                    break;
                }
            }

            if (iterations >= MAX_ITERATIONS)
            {
                MessageBox.Show($"警告：达到最大迭代次数{MAX_ITERATIONS}，排版可能不完整", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            return panelCount;
        }

        /// <summary>
        /// 从中间向两边排版
        /// </summary>
        private int GenerateCenterToSidesPanels(double minX, double maxX, double centerX)
        {
            int panelCount = 0;
            int leftIterations = 0;
            int rightIterations = 0;

            // 计算起始位置
            double leftStartX = centerX - _startDistance;
            double rightStartX = centerX + _startDistance;

            // 设置当前位置为起始位置
            double currentLeftX = leftStartX;
            double currentRightX = rightStartX;

            // 向左生成板子，添加循环保护
            while ((currentLeftX > minX || (_startDistance < 0 && currentLeftX > centerX + _startDistance)) && leftIterations < MAX_ITERATIONS)
            {
                // 计算下一个位置，保持完整板宽
                double nextLeftX = currentLeftX - _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(currentLeftX, nextLeftX);

                // 更新当前X位置
                currentLeftX = nextLeftX;
                leftIterations++;

                // 如果起始距离为正且当前位置已超出边界，则停止排版
                if (currentLeftX <= minX && _startDistance >= 0)
                {
                    break;
                }
            }

            // 向右生成板子，添加循环保护
            while ((currentRightX < maxX || (_startDistance < 0 && currentRightX < centerX - _startDistance)) && rightIterations < MAX_ITERATIONS)
            {
                // 计算下一个位置，保持完整板宽
                double nextRightX = currentRightX + _panelWidth;

                // 生成当前位置的板子
                panelCount += GeneratePanelAtPosition(currentRightX, nextRightX);

                // 更新当前X位置
                currentRightX = nextRightX;
                rightIterations++;

                // 如果起始距离为正且当前位置已超出边界，则停止排版
                if (currentRightX >= maxX && _startDistance >= 0)
                {
                    break;
                }
            }

            if (leftIterations >= MAX_ITERATIONS || rightIterations >= MAX_ITERATIONS)
            {
                MessageBox.Show($"警告：达到最大迭代次数{MAX_ITERATIONS}，排版可能不完整", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            return panelCount;
        }

        /// <summary>
        /// 在指定位置生成板子 - 核心智能避障算法
        /// </summary>
        private int GeneratePanelAtPosition(double panelMinX, double panelMaxX)
        {
            try
            {
                // 确保panelMinX <= panelMaxX
                if (panelMinX > panelMaxX)
                {
                    double temp = panelMinX;
                    panelMinX = panelMaxX;
                    panelMaxX = temp;
                }

                // 获取轮廓线在当前X范围内的Y范围
                var yRanges = GetYRangesInXRange(panelMinX, panelMaxX);
                if (yRanges.Count == 0)
                {
                    return 0; // 没有有效的Y范围
                }

                // 应用顶部和底部间隙
                yRanges = ApplyTopBottomGaps(yRanges);
                if (yRanges.Count == 0)
                {
                    return 0; // 应用间隙后没有有效范围
                }

                // 获取与当前板子位置相关的窗户和门洞口
                var relevantWindows = GetRelevantWindows(panelMinX, panelMaxX);
                var relevantDoors = GetRelevantDoors(panelMinX, panelMaxX);

                // 处理窗户 - 智能避障的核心逻辑
                foreach (var window in relevantWindows)
                {
                    if (IsRectCompletelyInsideHorizontally(panelMinX, panelMaxX, window.MinX, window.MaxX))
                    {
                        // 分割Y范围，避开窗户，并加上间隙
                        yRanges = SplitYRanges(yRanges, window.MinY - _windowGap, window.MaxY + _windowGap);
                    }
                }

                // 处理门洞口 - 智能避障
                foreach (var door in relevantDoors)
                {
                    if (IsRectCompletelyInsideHorizontally(panelMinX, panelMaxX, door.MinX, door.MaxX))
                    {
                        // 门洞口只在顶部加间隙，底部通常到地面
                        yRanges = SplitYRanges(yRanges, door.MinY, door.MaxY + _doorTopGap);
                    }
                }

                // 生成板子
                int panelCount = 0;
                foreach (var range in yRanges)
                {
                    double height = range.Item2 - range.Item1;
                    if (height >= MIN_PANEL_HEIGHT)
                    {
                        var panel = new Rectangle2D(panelMinX, range.Item1, panelMaxX, range.Item2);
                        _generatedPanels.Add(panel);
                        panelCount++;
                    }
                }

                return panelCount;
            }
            catch (Exception ex)
            {
                throw new GeometryCalculationException($"在位置({panelMinX:F2}, {panelMaxX:F2})生成板子时发生错误", ex);
            }
        }

        /// <summary>
        /// 获取指定X范围内的Y范围
        /// </summary>
        private List<Tuple<double, double>> GetYRangesInXRange(double minX, double maxX)
        {
            var allRanges = new List<Tuple<double, double>>();

            // 在X范围内采样多个点来获取Y范围
            int sampleCount = Math.Max(10, (int)Math.Ceiling((maxX - minX) / 10.0));
            double step = (maxX - minX) / sampleCount;

            for (int i = 0; i <= sampleCount; i++)
            {
                double x = minX + i * step;
                if (x > maxX) x = maxX;

                var ranges = _outline.GetYRangesAtX(x);
                allRanges.AddRange(ranges);
            }

            // 合并重叠的Y范围
            return MergeOverlappingRanges(allRanges);
        }

        /// <summary>
        /// 合并重叠的Y范围
        /// </summary>
        private List<Tuple<double, double>> MergeOverlappingRanges(List<Tuple<double, double>> ranges)
        {
            if (ranges.Count == 0)
                return new List<Tuple<double, double>>();

            // 按起始Y坐标排序
            ranges.Sort((a, b) => a.Item1.CompareTo(b.Item1));

            var merged = new List<Tuple<double, double>>();
            var current = ranges[0];

            for (int i = 1; i < ranges.Count; i++)
            {
                var next = ranges[i];

                // 检查是否重叠或相邻
                if (next.Item1 <= current.Item2 + TOLERANCE)
                {
                    // 合并范围
                    current = new Tuple<double, double>(current.Item1, Math.Max(current.Item2, next.Item2));
                }
                else
                {
                    // 添加当前范围并开始新的范围
                    merged.Add(current);
                    current = next;
                }
            }

            merged.Add(current);
            return merged;
        }

        /// <summary>
        /// 应用顶部和底部间隙
        /// </summary>
        private List<Tuple<double, double>> ApplyTopBottomGaps(List<Tuple<double, double>> ranges)
        {
            var adjustedRanges = new List<Tuple<double, double>>();

            foreach (var range in ranges)
            {
                double adjustedMin = range.Item1 + _bottomGap;
                double adjustedMax = range.Item2 - _topGap;

                if (adjustedMax - adjustedMin >= MIN_PANEL_HEIGHT)
                {
                    adjustedRanges.Add(new Tuple<double, double>(adjustedMin, adjustedMax));
                }
            }

            return adjustedRanges;
        }

        /// <summary>
        /// 获取与指定X范围相关的窗户
        /// </summary>
        private List<Rectangle2D> GetRelevantWindows(double minX, double maxX)
        {
            return _windows.Where(w => IsOverlappingHorizontally(minX, maxX, w.MinX, w.MaxX)).ToList();
        }

        /// <summary>
        /// 获取与指定X范围相关的门洞口
        /// </summary>
        private List<Rectangle2D> GetRelevantDoors(double minX, double maxX)
        {
            return _doors.Where(d => IsOverlappingHorizontally(minX, maxX, d.MinX, d.MaxX)).ToList();
        }
        #endregion
    }
}
