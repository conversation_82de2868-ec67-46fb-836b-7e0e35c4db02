namespace SmartRectangularPanelPlugin
{
    partial class SmartPanelForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxSelection = new System.Windows.Forms.GroupBox();
            this.lblDoorStatus = new System.Windows.Forms.Label();
            this.lblWindowStatus = new System.Windows.Forms.Label();
            this.lblOutlineStatus = new System.Windows.Forms.Label();
            this.btnSelectDoors = new System.Windows.Forms.Button();
            this.btnSelectWindows = new System.Windows.Forms.Button();
            this.btnSelectOutline = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBoxParameters = new System.Windows.Forms.GroupBox();
            this.txtDoorTopGap = new System.Windows.Forms.TextBox();
            this.txtWindowGap = new System.Windows.Forms.TextBox();
            this.txtBottomGap = new System.Windows.Forms.TextBox();
            this.txtTopGap = new System.Windows.Forms.TextBox();
            this.txtPanelWidth = new System.Windows.Forms.TextBox();
            this.txtStartDistance = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.groupBoxDirection = new System.Windows.Forms.GroupBox();
            this.rbCenterToSides = new System.Windows.Forms.RadioButton();
            this.rbRightToLeft = new System.Windows.Forms.RadioButton();
            this.rbLeftToRight = new System.Windows.Forms.RadioButton();
            this.groupBoxActions = new System.Windows.Forms.GroupBox();
            this.btnDeletePanels = new System.Windows.Forms.Button();
            this.btnGenerate = new System.Windows.Forms.Button();
            this.groupBoxStatistics = new System.Windows.Forms.GroupBox();
            this.lblStatistics = new System.Windows.Forms.Label();
            this.groupBoxSelection.SuspendLayout();
            this.groupBoxParameters.SuspendLayout();
            this.groupBoxDirection.SuspendLayout();
            this.groupBoxActions.SuspendLayout();
            this.groupBoxStatistics.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxSelection
            // 
            this.groupBoxSelection.Controls.Add(this.lblDoorStatus);
            this.groupBoxSelection.Controls.Add(this.lblWindowStatus);
            this.groupBoxSelection.Controls.Add(this.lblOutlineStatus);
            this.groupBoxSelection.Controls.Add(this.btnSelectDoors);
            this.groupBoxSelection.Controls.Add(this.btnSelectWindows);
            this.groupBoxSelection.Controls.Add(this.btnSelectOutline);
            this.groupBoxSelection.Controls.Add(this.label3);
            this.groupBoxSelection.Controls.Add(this.label2);
            this.groupBoxSelection.Controls.Add(this.label1);
            this.groupBoxSelection.Location = new System.Drawing.Point(12, 12);
            this.groupBoxSelection.Name = "groupBoxSelection";
            this.groupBoxSelection.Size = new System.Drawing.Size(360, 120);
            this.groupBoxSelection.TabIndex = 0;
            this.groupBoxSelection.TabStop = false;
            this.groupBoxSelection.Text = "对象选择";
            // 
            // lblDoorStatus
            // 
            this.lblDoorStatus.AutoSize = true;
            this.lblDoorStatus.Location = new System.Drawing.Point(280, 88);
            this.lblDoorStatus.Name = "lblDoorStatus";
            this.lblDoorStatus.Size = new System.Drawing.Size(41, 12);
            this.lblDoorStatus.TabIndex = 8;
            this.lblDoorStatus.Text = "0 个";
            // 
            // lblWindowStatus
            // 
            this.lblWindowStatus.AutoSize = true;
            this.lblWindowStatus.Location = new System.Drawing.Point(280, 58);
            this.lblWindowStatus.Name = "lblWindowStatus";
            this.lblWindowStatus.Size = new System.Drawing.Size(41, 12);
            this.lblWindowStatus.TabIndex = 7;
            this.lblWindowStatus.Text = "0 个";
            // 
            // lblOutlineStatus
            // 
            this.lblOutlineStatus.AutoSize = true;
            this.lblOutlineStatus.ForeColor = System.Drawing.Color.Red;
            this.lblOutlineStatus.Location = new System.Drawing.Point(280, 28);
            this.lblOutlineStatus.Name = "lblOutlineStatus";
            this.lblOutlineStatus.Size = new System.Drawing.Size(53, 12);
            this.lblOutlineStatus.TabIndex = 6;
            this.lblOutlineStatus.Text = "未选择";
            // 
            // btnSelectDoors
            // 
            this.btnSelectDoors.Location = new System.Drawing.Point(150, 83);
            this.btnSelectDoors.Name = "btnSelectDoors";
            this.btnSelectDoors.Size = new System.Drawing.Size(120, 23);
            this.btnSelectDoors.TabIndex = 5;
            this.btnSelectDoors.Text = "选择门洞口";
            this.btnSelectDoors.UseVisualStyleBackColor = true;
            this.btnSelectDoors.Click += new System.EventHandler(this.btnSelectDoors_Click);
            // 
            // btnSelectWindows
            // 
            this.btnSelectWindows.Location = new System.Drawing.Point(150, 53);
            this.btnSelectWindows.Name = "btnSelectWindows";
            this.btnSelectWindows.Size = new System.Drawing.Size(120, 23);
            this.btnSelectWindows.TabIndex = 4;
            this.btnSelectWindows.Text = "选择窗洞口";
            this.btnSelectWindows.UseVisualStyleBackColor = true;
            this.btnSelectWindows.Click += new System.EventHandler(this.btnSelectWindows_Click);
            // 
            // btnSelectOutline
            // 
            this.btnSelectOutline.Location = new System.Drawing.Point(150, 23);
            this.btnSelectOutline.Name = "btnSelectOutline";
            this.btnSelectOutline.Size = new System.Drawing.Size(120, 23);
            this.btnSelectOutline.TabIndex = 3;
            this.btnSelectOutline.Text = "选择轮廓线";
            this.btnSelectOutline.UseVisualStyleBackColor = true;
            this.btnSelectOutline.Click += new System.EventHandler(this.btnSelectOutline_Click);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(15, 88);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "门洞口：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(15, 58);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "窗洞口：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(15, 28);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "轮廓线：";
            // 
            // groupBoxParameters
            // 
            this.groupBoxParameters.Controls.Add(this.txtDoorTopGap);
            this.groupBoxParameters.Controls.Add(this.txtWindowGap);
            this.groupBoxParameters.Controls.Add(this.txtBottomGap);
            this.groupBoxParameters.Controls.Add(this.txtTopGap);
            this.groupBoxParameters.Controls.Add(this.txtPanelWidth);
            this.groupBoxParameters.Controls.Add(this.txtStartDistance);
            this.groupBoxParameters.Controls.Add(this.label9);
            this.groupBoxParameters.Controls.Add(this.label8);
            this.groupBoxParameters.Controls.Add(this.label7);
            this.groupBoxParameters.Controls.Add(this.label6);
            this.groupBoxParameters.Controls.Add(this.label5);
            this.groupBoxParameters.Controls.Add(this.label4);
            this.groupBoxParameters.Location = new System.Drawing.Point(12, 138);
            this.groupBoxParameters.Name = "groupBoxParameters";
            this.groupBoxParameters.Size = new System.Drawing.Size(360, 200);
            this.groupBoxParameters.TabIndex = 1;
            this.groupBoxParameters.TabStop = false;
            this.groupBoxParameters.Text = "排版参数";
            // 
            // txtDoorTopGap
            // 
            this.txtDoorTopGap.Location = new System.Drawing.Point(150, 168);
            this.txtDoorTopGap.Name = "txtDoorTopGap";
            this.txtDoorTopGap.Size = new System.Drawing.Size(100, 21);
            this.txtDoorTopGap.TabIndex = 11;
            this.txtDoorTopGap.Text = "5";
            this.txtDoorTopGap.TextChanged += new System.EventHandler(this.ParameterTextBox_TextChanged);
            // 
            // txtWindowGap
            // 
            this.txtWindowGap.Location = new System.Drawing.Point(150, 138);
            this.txtWindowGap.Name = "txtWindowGap";
            this.txtWindowGap.Size = new System.Drawing.Size(100, 21);
            this.txtWindowGap.TabIndex = 10;
            this.txtWindowGap.Text = "5";
            this.txtWindowGap.TextChanged += new System.EventHandler(this.ParameterTextBox_TextChanged);
            // 
            // txtBottomGap
            // 
            this.txtBottomGap.Location = new System.Drawing.Point(150, 108);
            this.txtBottomGap.Name = "txtBottomGap";
            this.txtBottomGap.Size = new System.Drawing.Size(100, 21);
            this.txtBottomGap.TabIndex = 9;
            this.txtBottomGap.Text = "10";
            this.txtBottomGap.TextChanged += new System.EventHandler(this.ParameterTextBox_TextChanged);
            // 
            // txtTopGap
            // 
            this.txtTopGap.Location = new System.Drawing.Point(150, 78);
            this.txtTopGap.Name = "txtTopGap";
            this.txtTopGap.Size = new System.Drawing.Size(100, 21);
            this.txtTopGap.TabIndex = 8;
            this.txtTopGap.Text = "5";
            this.txtTopGap.TextChanged += new System.EventHandler(this.ParameterTextBox_TextChanged);
            // 
            // txtPanelWidth
            // 
            this.txtPanelWidth.Location = new System.Drawing.Point(150, 48);
            this.txtPanelWidth.Name = "txtPanelWidth";
            this.txtPanelWidth.Size = new System.Drawing.Size(100, 21);
            this.txtPanelWidth.TabIndex = 7;
            this.txtPanelWidth.Text = "1000";
            this.txtPanelWidth.TextChanged += new System.EventHandler(this.ParameterTextBox_TextChanged);
            // 
            // txtStartDistance
            // 
            this.txtStartDistance.Location = new System.Drawing.Point(150, 18);
            this.txtStartDistance.Name = "txtStartDistance";
            this.txtStartDistance.Size = new System.Drawing.Size(100, 21);
            this.txtStartDistance.TabIndex = 6;
            this.txtStartDistance.Text = "0";
            this.txtStartDistance.TextChanged += new System.EventHandler(this.ParameterTextBox_TextChanged);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(15, 171);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(101, 12);
            this.label9.TabIndex = 5;
            this.label9.Text = "门顶部间隙：";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(15, 141);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(89, 12);
            this.label8.TabIndex = 4;
            this.label8.Text = "窗户间隙：";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(15, 111);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(89, 12);
            this.label7.TabIndex = 3;
            this.label7.Text = "底部间隙：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(15, 81);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(89, 12);
            this.label6.TabIndex = 2;
            this.label6.Text = "顶部间隙：";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(15, 51);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 1;
            this.label5.Text = "板宽：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(15, 21);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "起始距离：";
            // 
            // groupBoxDirection
            // 
            this.groupBoxDirection.Controls.Add(this.rbCenterToSides);
            this.groupBoxDirection.Controls.Add(this.rbRightToLeft);
            this.groupBoxDirection.Controls.Add(this.rbLeftToRight);
            this.groupBoxDirection.Location = new System.Drawing.Point(12, 344);
            this.groupBoxDirection.Name = "groupBoxDirection";
            this.groupBoxDirection.Size = new System.Drawing.Size(360, 80);
            this.groupBoxDirection.TabIndex = 2;
            this.groupBoxDirection.TabStop = false;
            this.groupBoxDirection.Text = "排版方向";
            // 
            // rbCenterToSides
            // 
            this.rbCenterToSides.AutoSize = true;
            this.rbCenterToSides.Location = new System.Drawing.Point(250, 30);
            this.rbCenterToSides.Name = "rbCenterToSides";
            this.rbCenterToSides.Size = new System.Drawing.Size(107, 16);
            this.rbCenterToSides.TabIndex = 2;
            this.rbCenterToSides.Text = "从中间向两边";
            this.rbCenterToSides.UseVisualStyleBackColor = true;
            this.rbCenterToSides.CheckedChanged += new System.EventHandler(this.DirectionRadioButton_CheckedChanged);
            // 
            // rbRightToLeft
            // 
            this.rbRightToLeft.AutoSize = true;
            this.rbRightToLeft.Location = new System.Drawing.Point(130, 30);
            this.rbRightToLeft.Name = "rbRightToLeft";
            this.rbRightToLeft.Size = new System.Drawing.Size(95, 16);
            this.rbRightToLeft.TabIndex = 1;
            this.rbRightToLeft.Text = "从右往左";
            this.rbRightToLeft.UseVisualStyleBackColor = true;
            this.rbRightToLeft.CheckedChanged += new System.EventHandler(this.DirectionRadioButton_CheckedChanged);
            // 
            // rbLeftToRight
            // 
            this.rbLeftToRight.AutoSize = true;
            this.rbLeftToRight.Checked = true;
            this.rbLeftToRight.Location = new System.Drawing.Point(15, 30);
            this.rbLeftToRight.Name = "rbLeftToRight";
            this.rbLeftToRight.Size = new System.Drawing.Size(95, 16);
            this.rbLeftToRight.TabIndex = 0;
            this.rbLeftToRight.TabStop = true;
            this.rbLeftToRight.Text = "从左往右";
            this.rbLeftToRight.UseVisualStyleBackColor = true;
            this.rbLeftToRight.CheckedChanged += new System.EventHandler(this.DirectionRadioButton_CheckedChanged);
            // 
            // groupBoxActions
            // 
            this.groupBoxActions.Controls.Add(this.btnDeletePanels);
            this.groupBoxActions.Controls.Add(this.btnGenerate);
            this.groupBoxActions.Location = new System.Drawing.Point(12, 430);
            this.groupBoxActions.Name = "groupBoxActions";
            this.groupBoxActions.Size = new System.Drawing.Size(360, 60);
            this.groupBoxActions.TabIndex = 3;
            this.groupBoxActions.TabStop = false;
            this.groupBoxActions.Text = "操作";
            // 
            // btnDeletePanels
            // 
            this.btnDeletePanels.Location = new System.Drawing.Point(200, 20);
            this.btnDeletePanels.Name = "btnDeletePanels";
            this.btnDeletePanels.Size = new System.Drawing.Size(120, 30);
            this.btnDeletePanels.TabIndex = 1;
            this.btnDeletePanels.Text = "删除板材";
            this.btnDeletePanels.UseVisualStyleBackColor = true;
            this.btnDeletePanels.Click += new System.EventHandler(this.btnDeletePanels_Click);
            // 
            // btnGenerate
            // 
            this.btnGenerate.Enabled = false;
            this.btnGenerate.Location = new System.Drawing.Point(40, 20);
            this.btnGenerate.Name = "btnGenerate";
            this.btnGenerate.Size = new System.Drawing.Size(120, 30);
            this.btnGenerate.TabIndex = 0;
            this.btnGenerate.Text = "生成板材";
            this.btnGenerate.UseVisualStyleBackColor = true;
            this.btnGenerate.Click += new System.EventHandler(this.btnGenerate_Click);
            // 
            // groupBoxStatistics
            // 
            this.groupBoxStatistics.Controls.Add(this.lblStatistics);
            this.groupBoxStatistics.Location = new System.Drawing.Point(12, 496);
            this.groupBoxStatistics.Name = "groupBoxStatistics";
            this.groupBoxStatistics.Size = new System.Drawing.Size(360, 100);
            this.groupBoxStatistics.TabIndex = 4;
            this.groupBoxStatistics.TabStop = false;
            this.groupBoxStatistics.Text = "统计信息";
            // 
            // lblStatistics
            // 
            this.lblStatistics.Location = new System.Drawing.Point(15, 20);
            this.lblStatistics.Name = "lblStatistics";
            this.lblStatistics.Size = new System.Drawing.Size(330, 70);
            this.lblStatistics.TabIndex = 0;
            this.lblStatistics.Text = "尚未生成板材";
            // 
            // SmartPanelForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(384, 608);
            this.Controls.Add(this.groupBoxStatistics);
            this.Controls.Add(this.groupBoxActions);
            this.Controls.Add(this.groupBoxDirection);
            this.Controls.Add(this.groupBoxParameters);
            this.Controls.Add(this.groupBoxSelection);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SmartPanelForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "智能矩形板排版插件 v2.0 - 支持复杂轮廓线";
            this.groupBoxSelection.ResumeLayout(false);
            this.groupBoxSelection.PerformLayout();
            this.groupBoxParameters.ResumeLayout(false);
            this.groupBoxParameters.PerformLayout();
            this.groupBoxDirection.ResumeLayout(false);
            this.groupBoxDirection.PerformLayout();
            this.groupBoxActions.ResumeLayout(false);
            this.groupBoxStatistics.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxSelection;
        private System.Windows.Forms.Label lblDoorStatus;
        private System.Windows.Forms.Label lblWindowStatus;
        private System.Windows.Forms.Label lblOutlineStatus;
        private System.Windows.Forms.Button btnSelectDoors;
        private System.Windows.Forms.Button btnSelectWindows;
        private System.Windows.Forms.Button btnSelectOutline;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBoxParameters;
        private System.Windows.Forms.TextBox txtDoorTopGap;
        private System.Windows.Forms.TextBox txtWindowGap;
        private System.Windows.Forms.TextBox txtBottomGap;
        private System.Windows.Forms.TextBox txtTopGap;
        private System.Windows.Forms.TextBox txtPanelWidth;
        private System.Windows.Forms.TextBox txtStartDistance;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.GroupBox groupBoxDirection;
        private System.Windows.Forms.RadioButton rbCenterToSides;
        private System.Windows.Forms.RadioButton rbRightToLeft;
        private System.Windows.Forms.RadioButton rbLeftToRight;
        private System.Windows.Forms.GroupBox groupBoxActions;
        private System.Windows.Forms.Button btnDeletePanels;
        private System.Windows.Forms.Button btnGenerate;
        private System.Windows.Forms.GroupBox groupBoxStatistics;
        private System.Windows.Forms.Label lblStatistics;
    }
}
