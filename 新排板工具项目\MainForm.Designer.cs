namespace SmartPanelTool
{
    partial class MainForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnSelectOutline = new System.Windows.Forms.Button();
            this.btnSelectWindow = new System.Windows.Forms.Button();
            this.btnSelectDoor = new System.Windows.Forms.Button();
            this.lblOutlineStatus = new System.Windows.Forms.Label();
            this.lblWindowStatus = new System.Windows.Forms.Label();
            this.lblDoorStatus = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.txtStartDistance = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.txtPanelWidth = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.txtTopGap = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.txtBottomGap = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.txtWindowGap = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.txtDoorTopGap = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.groupBoxDirection = new System.Windows.Forms.GroupBox();
            this.radioLeftToRight = new System.Windows.Forms.RadioButton();
            this.radioRightToLeft = new System.Windows.Forms.RadioButton();
            this.radioCenterToSides = new System.Windows.Forms.RadioButton();
            this.btnGenerate = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.label7 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBoxDirection.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnSelectOutline
            // 
            this.btnSelectOutline.Location = new System.Drawing.Point(12, 12);
            this.btnSelectOutline.Name = "btnSelectOutline";
            this.btnSelectOutline.Size = new System.Drawing.Size(100, 30);
            this.btnSelectOutline.TabIndex = 0;
            this.btnSelectOutline.Text = "选择轮廓线";
            this.btnSelectOutline.UseVisualStyleBackColor = true;
            this.btnSelectOutline.Click += new System.EventHandler(this.btnSelectOutline_Click);
            // 
            // btnSelectWindow
            // 
            this.btnSelectWindow.Location = new System.Drawing.Point(12, 48);
            this.btnSelectWindow.Name = "btnSelectWindow";
            this.btnSelectWindow.Size = new System.Drawing.Size(100, 30);
            this.btnSelectWindow.TabIndex = 1;
            this.btnSelectWindow.Text = "选择窗洞口";
            this.btnSelectWindow.UseVisualStyleBackColor = true;
            this.btnSelectWindow.Click += new System.EventHandler(this.btnSelectWindow_Click);
            // 
            // btnSelectDoor
            // 
            this.btnSelectDoor.Location = new System.Drawing.Point(12, 84);
            this.btnSelectDoor.Name = "btnSelectDoor";
            this.btnSelectDoor.Size = new System.Drawing.Size(100, 30);
            this.btnSelectDoor.TabIndex = 2;
            this.btnSelectDoor.Text = "选择门洞口";
            this.btnSelectDoor.UseVisualStyleBackColor = true;
            this.btnSelectDoor.Click += new System.EventHandler(this.btnSelectDoor_Click);
            // 
            // lblOutlineStatus
            // 
            this.lblOutlineStatus.AutoSize = true;
            this.lblOutlineStatus.ForeColor = System.Drawing.Color.Red;
            this.lblOutlineStatus.Location = new System.Drawing.Point(118, 21);
            this.lblOutlineStatus.Name = "lblOutlineStatus";
            this.lblOutlineStatus.Size = new System.Drawing.Size(77, 12);
            this.lblOutlineStatus.TabIndex = 3;
            this.lblOutlineStatus.Text = "未选择轮廓线";
            // 
            // lblWindowStatus
            // 
            this.lblWindowStatus.AutoSize = true;
            this.lblWindowStatus.ForeColor = System.Drawing.Color.Gray;
            this.lblWindowStatus.Location = new System.Drawing.Point(118, 57);
            this.lblWindowStatus.Name = "lblWindowStatus";
            this.lblWindowStatus.Size = new System.Drawing.Size(77, 12);
            this.lblWindowStatus.TabIndex = 4;
            this.lblWindowStatus.Text = "未选择窗洞口";
            // 
            // lblDoorStatus
            // 
            this.lblDoorStatus.AutoSize = true;
            this.lblDoorStatus.ForeColor = System.Drawing.Color.Gray;
            this.lblDoorStatus.Location = new System.Drawing.Point(118, 93);
            this.lblDoorStatus.Name = "lblDoorStatus";
            this.lblDoorStatus.Size = new System.Drawing.Size(77, 12);
            this.lblDoorStatus.TabIndex = 5;
            this.lblDoorStatus.Text = "未选择门洞口";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(6, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 6;
            this.label1.Text = "起始距离：";
            // 
            // txtStartDistance
            // 
            this.txtStartDistance.Location = new System.Drawing.Point(77, 22);
            this.txtStartDistance.Name = "txtStartDistance";
            this.txtStartDistance.Size = new System.Drawing.Size(80, 21);
            this.txtStartDistance.TabIndex = 7;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(6, 52);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 8;
            this.label2.Text = "板宽：";
            // 
            // txtPanelWidth
            // 
            this.txtPanelWidth.Location = new System.Drawing.Point(77, 49);
            this.txtPanelWidth.Name = "txtPanelWidth";
            this.txtPanelWidth.Size = new System.Drawing.Size(80, 21);
            this.txtPanelWidth.TabIndex = 9;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(6, 79);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 10;
            this.label3.Text = "顶部间隙：";
            // 
            // txtTopGap
            // 
            this.txtTopGap.Location = new System.Drawing.Point(77, 76);
            this.txtTopGap.Name = "txtTopGap";
            this.txtTopGap.Size = new System.Drawing.Size(80, 21);
            this.txtTopGap.TabIndex = 11;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(6, 106);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 12;
            this.label4.Text = "底部间隙：";
            // 
            // txtBottomGap
            // 
            this.txtBottomGap.Location = new System.Drawing.Point(77, 103);
            this.txtBottomGap.Name = "txtBottomGap";
            this.txtBottomGap.Size = new System.Drawing.Size(80, 21);
            this.txtBottomGap.TabIndex = 13;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(6, 133);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 14;
            this.label5.Text = "窗户间隙：";
            // 
            // txtWindowGap
            // 
            this.txtWindowGap.Location = new System.Drawing.Point(77, 130);
            this.txtWindowGap.Name = "txtWindowGap";
            this.txtWindowGap.Size = new System.Drawing.Size(80, 21);
            this.txtWindowGap.TabIndex = 15;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(6, 160);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(77, 12);
            this.label6.TabIndex = 16;
            this.label6.Text = "门顶部间隙：";
            // 
            // txtDoorTopGap
            // 
            this.txtDoorTopGap.Location = new System.Drawing.Point(89, 157);
            this.txtDoorTopGap.Name = "txtDoorTopGap";
            this.txtDoorTopGap.Size = new System.Drawing.Size(68, 21);
            this.txtDoorTopGap.TabIndex = 17;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.txtDoorTopGap);
            this.groupBox1.Controls.Add(this.txtStartDistance);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.txtWindowGap);
            this.groupBox1.Controls.Add(this.txtPanelWidth);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.txtBottomGap);
            this.groupBox1.Controls.Add(this.txtTopGap);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Location = new System.Drawing.Point(12, 120);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(170, 190);
            this.groupBox1.TabIndex = 18;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "参数设置";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.groupBoxDirection);
            this.groupBox2.Location = new System.Drawing.Point(200, 120);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(170, 190);
            this.groupBox2.TabIndex = 19;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "排版设置";
            // 
            // groupBoxDirection
            // 
            this.groupBoxDirection.Controls.Add(this.radioCenterToSides);
            this.groupBoxDirection.Controls.Add(this.radioRightToLeft);
            this.groupBoxDirection.Controls.Add(this.radioLeftToRight);
            this.groupBoxDirection.Location = new System.Drawing.Point(6, 20);
            this.groupBoxDirection.Name = "groupBoxDirection";
            this.groupBoxDirection.Size = new System.Drawing.Size(158, 100);
            this.groupBoxDirection.TabIndex = 0;
            this.groupBoxDirection.TabStop = false;
            this.groupBoxDirection.Text = "排版方向";
            // 
            // radioLeftToRight
            // 
            this.radioLeftToRight.AutoSize = true;
            this.radioLeftToRight.Location = new System.Drawing.Point(6, 20);
            this.radioLeftToRight.Name = "radioLeftToRight";
            this.radioLeftToRight.Size = new System.Drawing.Size(83, 16);
            this.radioLeftToRight.TabIndex = 0;
            this.radioLeftToRight.TabStop = true;
            this.radioLeftToRight.Text = "从左往右排";
            this.radioLeftToRight.UseVisualStyleBackColor = true;
            // 
            // radioRightToLeft
            // 
            this.radioRightToLeft.AutoSize = true;
            this.radioRightToLeft.Location = new System.Drawing.Point(6, 42);
            this.radioRightToLeft.Name = "radioRightToLeft";
            this.radioRightToLeft.Size = new System.Drawing.Size(83, 16);
            this.radioRightToLeft.TabIndex = 1;
            this.radioRightToLeft.TabStop = true;
            this.radioRightToLeft.Text = "从右往左排";
            this.radioRightToLeft.UseVisualStyleBackColor = true;
            // 
            // radioCenterToSides
            // 
            this.radioCenterToSides.AutoSize = true;
            this.radioCenterToSides.Location = new System.Drawing.Point(6, 64);
            this.radioCenterToSides.Name = "radioCenterToSides";
            this.radioCenterToSides.Size = new System.Drawing.Size(107, 16);
            this.radioCenterToSides.TabIndex = 2;
            this.radioCenterToSides.TabStop = true;
            this.radioCenterToSides.Text = "从中间向两边排";
            this.radioCenterToSides.UseVisualStyleBackColor = true;
            // 
            // btnGenerate
            // 
            this.btnGenerate.Location = new System.Drawing.Point(200, 320);
            this.btnGenerate.Name = "btnGenerate";
            this.btnGenerate.Size = new System.Drawing.Size(80, 35);
            this.btnGenerate.TabIndex = 20;
            this.btnGenerate.Text = "生成矩形板";
            this.btnGenerate.UseVisualStyleBackColor = true;
            this.btnGenerate.Click += new System.EventHandler(this.btnGenerate_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(290, 320);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(80, 35);
            this.btnCancel.TabIndex = 21;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label7.ForeColor = System.Drawing.Color.Blue;
            this.label7.Location = new System.Drawing.Point(250, 12);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(120, 16);
            this.label7.TabIndex = 22;
            this.label7.Text = "智能排板工具";
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(384, 371);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnGenerate);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.lblDoorStatus);
            this.Controls.Add(this.lblWindowStatus);
            this.Controls.Add(this.lblOutlineStatus);
            this.Controls.Add(this.btnSelectDoor);
            this.Controls.Add(this.btnSelectWindow);
            this.Controls.Add(this.btnSelectOutline);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "MainForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "智能排板工具 v1.0";
            this.Load += new System.EventHandler(this.MainForm_Load);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBoxDirection.ResumeLayout(false);
            this.groupBoxDirection.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnSelectOutline;
        private System.Windows.Forms.Button btnSelectWindow;
        private System.Windows.Forms.Button btnSelectDoor;
        private System.Windows.Forms.Label lblOutlineStatus;
        private System.Windows.Forms.Label lblWindowStatus;
        private System.Windows.Forms.Label lblDoorStatus;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtStartDistance;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtPanelWidth;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox txtTopGap;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox txtBottomGap;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtWindowGap;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox txtDoorTopGap;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBoxDirection;
        private System.Windows.Forms.RadioButton radioCenterToSides;
        private System.Windows.Forms.RadioButton radioRightToLeft;
        private System.Windows.Forms.RadioButton radioLeftToRight;
        private System.Windows.Forms.Button btnGenerate;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label label7;
    }
}
